<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI招聘客服管理系统 - 触发器管理</title>
    <link rel="stylesheet" href="/static/css/all.min.css">
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/triggers.css">
    <style>
        /* 额外的样式 */
        .search-container {
            margin-bottom: 15px;
            display: flex;
        }
        .search-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .pagination-container {
            margin-top: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .pagination-info {
            font-size: 14px;
            color: #666;
        }
        .pagination-current {
            margin: 0 8px;
        }
        .empty-row {
            height: 42px;
            border-bottom: 1px solid #f3f3f3;
        }
        .table-container {
            min-height: 420px; /* 10条记录的大约高度 */
        }
        .prompt-card {
            margin-top: 15px;
        }
        .prompt-textarea {
            width: 100%;
            min-height: 150px;
            resize: vertical;
            padding: 10px;
            font-size: 14px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .card-footer {
            padding: 12px 15px;
            border-top: 1px solid #eaeaea;
            display: flex;
            justify-content: flex-end;
        }
    </style>
</head>
<body>
    <div class="app-wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>AI招聘客服管理系统</h2>
            </div>
            <div class="sidebar-menu">
                <div class="sidebar-menu-item" data-link="index">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表盘</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-briefcase"></i>
                    <span>职位管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="job_classification">
                        <i class="fas fa-tags"></i>
                        <span>职位分类</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="batch_management">
                        <i class="fas fa-layer-group"></i>
                        <span>批量管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="positions">
                        <i class="fas fa-list"></i>
                        <span>职位列表</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="candidates">
                    <i class="fas fa-user-tie"></i>
                    <span>候选人管理</span>
                </div>
                <div class="sidebar-menu-item" data-link="virtual_hr">
                    <i class="fas fa-robot"></i>
                    <span>虚拟HR</span>
                </div>
                <div class="sidebar-menu-item" data-link="scenes">
                    <i class="fas fa-tasks"></i>
                    <span>面试场景</span>
                </div>
                <div class="sidebar-menu-item" data-link="interview">
                    <i class="fas fa-comments"></i>
                    <span>面试互动（仅供测试）</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-star"></i>
                    <span>胜任力模型</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item active" data-link="triggers">
                        <i class="fas fa-bolt"></i>
                        <span>触发器</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="classifier">
                        <i class="fas fa-filter"></i>
                        <span>分类器</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="ai_brain">
                    <i class="fas fa-brain"></i>
                    <span>AI大脑</span>
                </div>
                <div class="sidebar-menu-item has-submenu open">
                    <i class="fas fa-chart-line"></i>
                    <span>AI数据透视</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu show">
                    <div class="sidebar-menu-item active" data-link="scheduled_analysis">
                        <i class="fas fa-clock"></i>
                        <span>客情分析</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="qa_engine">
                        <i class="fas fa-question-circle"></i>
                        <span>QA引擎</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-key"></i>
                    <span>APIKey管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="apikey_create">
                        <i class="fas fa-plus"></i>
                        <span>创建APIKey</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="apikey_usage">
                        <i class="fas fa-chart-bar"></i>
                        <span>用量统计</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-cog"></i>
                    <span>用户设置</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="channel_management">
                        <i class="fas fa-plug"></i>
                        <span>渠道接入管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="global_settings">
                        <i class="fas fa-sliders-h"></i>
                        <span>全局配置</span>
                    </div>
                </div>
                <div class="sidebar-menu-item admin-only" data-link="user_management07082">
                    <i class="fas fa-user-cog"></i>
                    <span>用户管理</span>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部导航栏 -->
            <div class="topbar">
                <div class="topbar-toggle">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="topbar-title">
                    触发器管理
                </div>
                
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <div class="page-header">
                    <h2>触发器管理</h2>
                </div>
                
                <div class="trigger-panels">
                    <div class="panel-column">
                        <!-- 分数触发器面板 -->
                        <div class="card">
                            <div class="card-header">
                                <h3>分数触发器</h3>
                                <button class="btn btn-primary" id="addScoreTriggerBtn">
                                    <i class="fas fa-plus"></i> 新增分数触发器
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="search-container">
                                    <input type="text" class="search-input" id="scoreSearchInput" placeholder="搜索分数触发器...">
                                </div>
                                <div class="table-container">
                                    <table class="table" id="scoreTriggerTable">
                                        <thead>
                                            <tr>
                                                <th width="10%">序号</th>
                                                <th width="30%">触发器名称</th>
                                                <th width="40%">动作</th>
                                                <th width="20%">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- 分数触发器列表将通过JS动态生成 -->
                                        </tbody>
                                    </table>
                                </div>
                                <div class="pagination-container" id="scorePagination">
                                    <!-- 分页控件将通过JS动态生成 -->
                                </div>
                            </div>
                        </div>

                        <!-- 分数触发器提示卡片 -->
                        <div class="card prompt-card" id="scoreTriggerPromptCard">
                            <div class="card-header">
                                <h3>分数触发器提示词</h3>
                            </div>
                            <div class="card-body">
                                <span>可用变量及含义：{score_trigger_name}分数触发器名字,{explanation}说明,{score}触发分数,{action}触发动作,{repeated_triggering}是否可重复执行</span>
                            </div>
                            <div class="card-body">
                                <textarea id="scoreTriggerPrompt" class="prompt-textarea" placeholder="添加分数触发器提示..."></textarea>
                            </div>
                            <div class="card-footer">
                                <button class="btn btn-primary" id="saveScoreTriggerPromptBtn">保存提示</button>
                            </div>
                        </div>
                    </div>

                    <div class="panel-column">
                        <!-- 语义触发器面板 -->
                        <div class="card">
                            <div class="card-header">
                                <h3>语义触发器</h3>
                                <button class="btn btn-primary" id="addSemanticTriggerBtn">
                                    <i class="fas fa-plus"></i> 新增语义触发器
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="search-container">
                                    <input type="text" class="search-input" id="semanticSearchInput" placeholder="搜索语义触发器...">
                                </div>
                                <div class="table-container">
                                    <table class="table" id="semanticTriggerTable">
                                        <thead>
                                            <tr>
                                                <th width="10%">序号</th>
                                                <th width="30%">触发器名称</th>
                                                <th width="40%">触发动作</th>
                                                <th width="20%">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- 语义触发器列表将通过JS动态生成 -->
                                        </tbody>
                                    </table>
                                </div>
                                <div class="pagination-container" id="semanticPagination">
                                    <!-- 分页控件将通过JS动态生成 -->
                                </div>
                            </div>
                        </div>

                        <!-- 语义触发器提示卡片 -->
                        <div class="card prompt-card" id="semanticTriggerPromptCard">
                            <div class="card-header">
                                <h3>语义触发器提示词</h3>
                            </div>
                            <div class="card-body">
                                <span>可用变量及含义：{semantic_trigger_name}语义触发器名字,{explanation}说明,{semantic_content}语义内容,{action}触发动作,{repeated_triggering}是否可重复执行</span>
                            </div>
                            <div class="card-body">
                                <textarea id="semanticTriggerPrompt" class="prompt-textarea" placeholder="添加语义触发器提示..."></textarea>
                            </div>
                            <div class="card-footer">
                                <button class="btn btn-primary" id="saveSemanticTriggerPromptBtn">保存提示</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 分数触发器模态框 -->
    <div class="modal" id="scoreTriggerModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="scoreTriggerModalTitle">新增分数触发器</h3>
                <span class="modal-close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="scoreTriggerForm" data-validate="true">
                    <input type="hidden" id="scoreTriggerID" name="id">
                    
                    <div class="form-group">
                        <label for="score_trigger_name" class="form-label">触发器名称:</label>
                        <input type="text" id="score_trigger_name" name="score_trigger_name" class="form-control" required>
                        <small>给触发器起个易于识别的名称</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="score" class="form-label">触发分数:</label>
                        <input type="number" id="score" name="score" class="form-control" required>
                        <small>当候选人得分达到此分数时，将触发指定动作</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="action" class="form-label">触发动作:</label>
                        <input type="text" id="action" name="action" class="form-control" required>
                        <small>例如：发送邮件、切换场景等</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="score_explanation" class="form-label">触发器说明:</label>
                        <textarea id="score_explanation" name="explanation" class="form-control" rows="3"></textarea>
                        <small>对此触发器的详细说明（可选）</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="repeated_triggering" class="form-label">重复触发:</label>
                        <select id="repeated_triggering" name="repeated_triggering" class="form-control">
                            <option value="0">不重复触发</option>
                            <option value="1">允许重复触发</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelScoreTriggerBtn">取消</button>
                <button class="btn btn-primary" id="saveScoreTriggerBtn">保存</button>
            </div>
        </div>
    </div>

    <!-- 语义触发器模态框 -->
    <div class="modal" id="semanticTriggerModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="semanticTriggerModalTitle">新增语义触发器</h3>
                <span class="modal-close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="semanticTriggerForm" data-validate="true">
                    <input type="hidden" id="semanticTriggerID" name="id">
                    
                    <div class="form-group">
                        <label for="semantic_trigger_name" class="form-label">触发器名称:</label>
                        <input type="text" id="semantic_trigger_name" name="semantic_trigger_name" class="form-control" required>
                        <small>给触发器起个易于识别的名称</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="semantic_content" class="form-label">语义内容:</label>
                        <textarea id="semantic_content" name="semantic_content" class="form-control" rows="4" required></textarea>
                        <small>当候选人回答包含此语义内容时，将触发指定动作</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="semantic_action" class="form-label">触发动作:</label>
                        <input type="text" id="semantic_action" name="action" class="form-control" required>
                        <small>例如：发送邮件、切换场景等</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="semantic_explanation" class="form-label">触发器说明:</label>
                        <textarea id="semantic_explanation" name="explanation" class="form-control" rows="3"></textarea>
                        <small>对此触发器的详细说明（可选）</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="semantic_repeated_triggering" class="form-label">重复触发:</label>
                        <select id="semantic_repeated_triggering" name="repeated_triggering" class="form-control">
                            <option value="0">不重复触发</option>
                            <option value="1">允许重复触发</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelSemanticTriggerBtn">取消</button>
                <button class="btn btn-primary" id="saveSemanticTriggerBtn">保存</button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/static/js/main.js"></script>
    <script src="/static/js/triggers.js"></script>
    <script src="/static/js/sweetalert2@11"></script>
    <script src="/static/js/auth07082.js"></script>
    <script src="/static/js/auth-check.js"></script>
    <script src="/static/js/user-logout.js"></script>
</body>
</html> 