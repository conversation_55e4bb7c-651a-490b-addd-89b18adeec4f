<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI招聘客服管理系统 - 候选人管理</title>
    <link rel="stylesheet" href="/static/css/all.min.css">
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/candidates.css">
</head>
<body>
    <div class="app-wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>AI招聘客服管理系统</h2>
            </div>
            <div class="sidebar-menu">
                <div class="sidebar-menu-item" data-link="index">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表盘</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-briefcase"></i>
                    <span>职位管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="job_classification">
                        <i class="fas fa-tags"></i>
                        <span>职位分类</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="batch_management">
                        <i class="fas fa-layer-group"></i>
                        <span>批量管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="positions">
                        <i class="fas fa-list"></i>
                        <span>职位列表</span>
                    </div>
                </div>
                <div class="sidebar-menu-item active" data-link="candidates">
                    <i class="fas fa-user-tie"></i>
                    <span>候选人管理</span>
                </div>
                <div class="sidebar-menu-item" data-link="virtual_hr">
                    <i class="fas fa-robot"></i>
                    <span>虚拟HR</span>
                </div>
                <div class="sidebar-menu-item" data-link="scenes">
                    <i class="fas fa-tasks"></i>
                    <span>面试场景</span>
                </div>
                <div class="sidebar-menu-item" data-link="interview">
                    <i class="fas fa-comments"></i>
                    <span>面试互动（仅供测试）</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-star"></i>
                    <span>胜任力模型</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="triggers">
                        <i class="fas fa-bolt"></i>
                        <span>触发器</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="classifier">
                        <i class="fas fa-filter"></i>
                        <span>分类器</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="ai_brain">
                    <i class="fas fa-brain"></i>
                    <span>AI大脑</span>
                </div>
                <div class="sidebar-menu-item has-submenu open">
                    <i class="fas fa-chart-line"></i>
                    <span>AI数据透视</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu show">
                    <div class="sidebar-menu-item active" data-link="scheduled_analysis">
                        <i class="fas fa-clock"></i>
                        <span>客情分析</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="qa_engine">
                        <i class="fas fa-question-circle"></i>
                        <span>QA引擎</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-key"></i>
                    <span>APIKey管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="apikey_create">
                        <i class="fas fa-plus"></i>
                        <span>创建APIKey</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="apikey_usage">
                        <i class="fas fa-chart-bar"></i>
                        <span>用量统计</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-cog"></i>
                    <span>用户设置</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="channel_management">
                        <i class="fas fa-plug"></i>
                        <span>渠道接入管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="global_settings">
                        <i class="fas fa-sliders-h"></i>
                        <span>全局配置</span>
                    </div>
                </div>
                <div class="sidebar-menu-item admin-only" data-link="user_management07082">
                    <i class="fas fa-user-cog"></i>
                    <span>用户管理</span>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部导航栏 -->
            <div class="topbar">
                <div class="topbar-toggle">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="topbar-title">
                    候选人管理
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 页面标题和操作 -->
                <div class="page-header">
                    <h2>候选人列表</h2>
                    <button class="btn btn-primary" id="addCandidateBtn">
                        <i class="fas fa-plus"></i> 新增候选人
                    </button>
                </div>

                <!-- 搜索和筛选 -->
                <div class="card mb-20">
                    <div class="card-body">
                        <div class="filter-container">
                            <div class="search-box">
                                <input type="text" id="searchInput" class="form-control" placeholder="搜索姓名、手机号...">
                                <button class="btn btn-primary" id="searchBtn">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                            </div>
                            <div class="filter-options">
                                <div class="form-group">
                                    <label for="positionFilter" class="form-label">应聘职位:</label>
                                    <select id="positionFilter" class="form-control">
                                        <option value="">全部</option>
                                        <!-- 职位选项将通过JS动态添加 -->
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="sortBy" class="form-label">排序方式:</label>
                                    <select id="sortBy" class="form-control">
                                        <option value="newest">最新添加</option>
                                        <option value="name">姓名</option>
                                        <option value="score">评分</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 候选人列表 -->
                <div class="card">
                    <div class="card-body">
                        <div class="candidate-list" id="candidateList">
                            <!-- 候选人列表将通过JS动态生成 -->
                            <div class="candidate-list-loading">
                                <p>加载中...</p>
                            </div>
                        </div>
                        <!-- 分页 -->
                        <div class="pagination-container" id="pagination">
                            <!-- 分页将通过JS动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增/编辑候选人模态框 -->
    <div class="modal" id="candidateModal">
        <div class="modal-content candidate-form-modal">
            <div class="modal-header">
                <h3 id="modalTitle">新增候选人</h3>
                <span class="modal-close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="candidateForm" data-validate="true">
                    <input type="hidden" id="candidateId" name="userid">
                    
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label for="positionId" class="form-label">应聘职位:</label>
                                <select id="positionId" name="positionId" class="form-control" required>
                                    <option value="">请选择</option>
                                    <!-- 职位选项将通过JS动态添加 -->
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="name" class="form-label">姓名:</label>
                                <input type="text" id="name" name="name" class="form-control">
                            </div>
                            
                            <div class="form-group">
                                <label for="real_name" class="form-label">真实姓名:</label>
                                <input type="text" id="real_name" name="real_name" class="form-control">
                            </div>
                            
                            <div class="form-group">
                                <label for="title" class="form-label">称呼:</label>
                                <input type="text" id="title" name="title" class="form-control" placeholder="如：张先生、李小姐">
                            </div>
                            
                            <div class="form-group">
                                <label for="gender" class="form-label">性别:</label>
                                <select id="gender" name="gender" class="form-control">
                                    <option value="">请选择</option>
                                    <option value="男">男</option>
                                    <option value="女">女</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="age" class="form-label">年龄:</label>
                                <input type="number" id="age" name="age" class="form-control" min="16" max="100">
                            </div>
                            
                            <div class="form-group">
                                <label for="education_experience" class="form-label">最高学历:</label>
                                <select id="education_experience" name="education_experience" class="form-control">
                                    <option value="">请选择</option>
                                    <option value="高中">高中</option>
                                    <option value="大专">大专</option>
                                    <option value="本科">本科</option>
                                    <option value="硕士">硕士</option>
                                    <option value="博士">博士</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="graduate_school" class="form-label">毕业院校:</label>
                                <input type="text" id="graduate_school" name="graduate_school" class="form-control">
                            </div>
                        </div>
                        
                        <div class="form-col">
                            <div class="form-group">
                                <label for="phone" class="form-label">手机号码:</label>
                                <input type="tel" id="phone" name="phone" class="form-control">
                            </div>
                            
                            <div class="form-group">
                                <label for="wechat" class="form-label">微信:</label>
                                <input type="text" id="wechat" name="wechat" class="form-control">
                            </div>
                            
                            <div class="form-group">
                                <label for="wechat_nickname" class="form-label">微信昵称:</label>
                                <input type="text" id="wechat_nickname" name="wechat_nickname" class="form-control">
                            </div>
                            
                            <div class="form-group">
                                <label for="city" class="form-label">城市:</label>
                                <input type="text" id="city" name="city" class="form-control">
                            </div>
                            
                            <div class="form-group">
                                <label for="address" class="form-label">地址:</label>
                                <input type="text" id="address" name="address" class="form-control">
                            </div>
                            
                            <div class="form-group">
                                <label for="source" class="form-label">来源:</label>
                                <select id="source" name="source" class="form-control">
                                    <option value="boss">boss直聘</option>
                                    <option value="wechat">微信</option>
                                    <option value="other">其他</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="resume" class="form-label">简历链接:</label>
                        <input type="text" id="resume" name="resume" class="form-control">
                    </div>
                    
                    <div class="form-group">
                        <label for="resume_text" class="form-label">文本简历:</label>
                        <textarea id="resume_text" name="resume_text" class="form-control" rows="5" placeholder="粘贴候选人的简历文本"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="concerns" class="form-label">用户痛点:</label>
                        <div id="concernsContainer" class="concerns-container">
                            <div class="concerns-list" id="concernsList">
                                <!-- 痛点列表将通过JS动态生成 -->
                            </div>
                            <div class="concerns-input-group">
                                <input type="text" id="concernsInput" class="form-control" placeholder="输入新的用户痛点">
                                <button type="button" id="addConcernBtn" class="btn btn-secondary btn-sm">
                                    <i class="fas fa-plus"></i> 添加
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="remarks" class="form-label">备注:</label>
                        <textarea id="remarks" name="remarks" class="form-control" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-light" id="cancelBtn">取消</button>
                <button class="btn btn-primary" id="saveCandidateBtn">保存</button>
            </div>
        </div>
    </div>

    <!-- 候选人详情模态框 -->
    <div class="modal" id="candidateDetailModal">
        <div class="modal-content candidate-form-modal">
            <div class="modal-header">
                <h3 id="detailModalTitle">候选人详情</h3>
                <span class="modal-close">&times;</span>
            </div>
            <div class="modal-body">
                <div id="candidateDetails">
                    <!-- 候选人详情将通过JS动态生成 -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-light" id="closeDetailBtn">关闭</button>
                <button class="btn btn-primary" id="editCandidateBtn">编辑</button>
                <!-- <button class="btn btn-success" id="startInterviewBtn">开始面试</button> -->
            </div>
        </div>
    </div>

    <!-- 脚本引用 -->
    <script src="/static/js/main.js"></script>
    <script src="/static/js/api.js"></script>
    <script src="/static/js/candidates.js"></script>
    <script src="/static/js/sweetalert2@11"></script>
    <script src="/static/js/auth07082.js"></script>
    <script src="/static/js/auth-check.js"></script>
    <script src="/static/js/user-logout.js"></script>
</body>
</html> 