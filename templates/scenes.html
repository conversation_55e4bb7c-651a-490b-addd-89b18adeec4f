<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI招聘客服管理系统 - 面试场景</title>
    <link rel="stylesheet" href="/static/css/all.min.css">
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/scenes.css">
    <link rel="stylesheet" href="/static/css/flowchart.css">
    <link rel="stylesheet" href="/static/css/select2.min.css" />
    <link rel="stylesheet" href="/static/css/bootstrap.min.css">
    <style>
        /* 基础字体大小设置，确保与其他页面一致 */
        body {
            font-size: 14px;
        }
        
        修复sidebar-header中h2字体大小问题
        .sidebar-header h2 {
            font-size: 1.25rem; /* 20px，与其他页面保持一致 */
        }
        
        /* 自定义样式，增强问题弹窗显示效果 */
        #addQuestionModal .modal-content {
            border-radius: 6px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        #addQuestionModal .modal-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #eee;
            padding: 15px 20px;
        }
        
        #addQuestionModal .modal-body {
            padding: 20px;
        }
        
        #addQuestionModal textarea {
            font-size: 15px;
            line-height: 1.5;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
        }
        
        #addQuestionModal textarea:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }
        
        #addQuestionModal .form-group {
            margin-bottom: 20px;
        }
        
        #addQuestionModal .form-label {
            font-weight: 500;
            margin-bottom: 8px;
            display: block;
        }
        
        /* 增强按钮样式 */
        .add-question-btn {
            background-color: #f8f9fa;
            border: 1px dashed #ccc;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            cursor: pointer;
            text-align: center;
            transition: all 0.2s;
        }
        
        .add-question-btn:hover {
            background-color: #e9ecef;
            border-color: #aaa;
        }

        /* Select2 自定义样式 */
        .select2-container--default {
            width: 100% !important;
        }
        .select2-container--default .select2-selection--multiple {
            border: 1px solid #ced4da;
            border-radius: 0.25rem;
            min-height: 38px;
            padding: 2px 5px;
        }
        .select2-container--default .select2-selection--multiple .select2-selection__choice {
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 0.25rem;
            padding: 0.2rem;
            padding-left: 30px;
            padding-right: 10px;
            margin: 3px;
            position: relative;
        }
        .select2-container--default .select2-selection--multiple .select2-selection__choice__display {
            margin-left: 0;
            padding: 0;
        }
        .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
            position: absolute;
            left: 5px;
            top: 50%;
            transform: translateY(-50%);
            color: #fff;
            background-color: #dc3545;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            line-height: 16px;
            text-align: center;
            font-size: 12px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0;
        }
        .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
            background-color: #c82333;
            color: white;
            cursor: pointer;
        }

        /* 场景内容布局样式 */
        .scene-content {
            display: none;
            padding: 15px;
            background-color: #f8f9fa;
            border-top: 1px solid #eee;
        }

        .scene-content.expanded {
            display: block;
        }

        .scene-info {
            margin-bottom: 15px;
        }

        .scene-info-table {
            width: 100%;
            margin-bottom: 15px;
        }

        .scene-info-table td {
            padding: 6px 12px;
            vertical-align: top;
        }

        .scene-info-table td:first-child {
            width: 150px;
            font-weight: 500;
            color: #495057;
        }

        .scene-info-row {
            display: flex;
            flex-wrap: wrap;
        }

        .scene-info-col {
            flex: 0 0 50%;
            max-width: 50%;
            margin-bottom: 10px;
        }

        @media (max-width: 992px) {
            .scene-info-col {
                flex: 0 0 100%;
                max-width: 100%;
            }
        }

        .scene-questions {
            border-top: 1px solid #ddd;
            padding-top: 15px;
        }
    </style>
</head>
<body>
    <div class="app-wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>AI招聘客服管理系统</h2>
            </div>
            <div class="sidebar-menu">
                <div class="sidebar-menu-item" data-link="index">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表盘</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-briefcase"></i>
                    <span>职位管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="job_classification">
                        <i class="fas fa-tags"></i>
                        <span>职位分类</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="batch_management">
                        <i class="fas fa-layer-group"></i>
                        <span>批量管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="positions">
                        <i class="fas fa-list"></i>
                        <span>职位列表</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="candidates">
                    <i class="fas fa-user-tie"></i>
                    <span>候选人管理</span>
                </div>
                <div class="sidebar-menu-item" data-link="virtual_hr">
                    <i class="fas fa-robot"></i>
                    <span>虚拟HR</span>
                </div>
                <div class="sidebar-menu-item active" data-link="scenes">
                    <i class="fas fa-tasks"></i>
                    <span>面试场景</span>
                </div>
                <div class="sidebar-menu-item" data-link="interview">
                    <i class="fas fa-comments"></i>
                    <span>面试互动（仅供测试）</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-star"></i>
                    <span>胜任力模型</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="triggers">
                        <i class="fas fa-bolt"></i>
                        <span>触发器</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="classifier">
                        <i class="fas fa-filter"></i>
                        <span>分类器</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="ai_brain">
                    <i class="fas fa-brain"></i>
                    <span>AI大脑</span>
                </div>
                <div class="sidebar-menu-item has-submenu open">
                    <i class="fas fa-chart-line"></i>
                    <span>AI数据透视</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu show">
                    <div class="sidebar-menu-item active" data-link="scheduled_analysis">
                        <i class="fas fa-clock"></i>
                        <span>客情分析</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="qa_engine">
                        <i class="fas fa-question-circle"></i>
                        <span>QA引擎</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-key"></i>
                    <span>APIKey管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="apikey_create">
                        <i class="fas fa-plus"></i>
                        <span>创建APIKey</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="apikey_usage">
                        <i class="fas fa-chart-bar"></i>
                        <span>用量统计</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-cog"></i>
                    <span>用户设置</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="channel_management">
                        <i class="fas fa-plug"></i>
                        <span>渠道接入管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="global_settings">
                        <i class="fas fa-sliders-h"></i>
                        <span>全局配置</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-cogs"></i>
                    <span>系统设置</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item admin-only" data-link="user_management07082">
                        <i class="fas fa-user-cog"></i>
                        <span>用户管理</span>
                    </div>
                    <div class="sidebar-menu-item admin-only" data-link="system_config">
                        <i class="fas fa-wrench"></i>
                        <span>系统配置</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部导航栏 -->
            <div class="topbar">
                <div class="topbar-toggle">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="topbar-title">
                    面试场景
                </div>
                
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <div class="scenes-container">
                    <!-- 左侧岗位列表 -->
                    <div class="positions-panel">
                        <div class="panel-header">
                            <h3>职位分类列表</h3>
                        </div>
                        <div class="panel-body">
                            <div class="positions-list" id="positionsList">
                                <!-- 职位分类列表将通过JS动态生成 -->
                                <div class="positions-list-loading">
                                    <p>加载中...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧流程图设计器 -->
                    <div class="scenes-panel">
                        <div class="panel-header">
                            <h3 id="currentPositionName">请选择职位分类</h3>
                            <div class="flowchart-toolbar">
                                <button id="addSceneBtn" class="btn btn-primary" disabled>
                                    <i class="fas fa-plus"></i> 添加场景
                                </button>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-outline-primary active" id="flowchartViewBtn" disabled>
                                        <i class="fas fa-project-diagram"></i> 流程图
                                    </button>
                                    <button class="btn btn-outline-primary" id="listViewBtn" disabled>
                                        <i class="fas fa-list"></i> 列表
                                    </button>
                                </div>
                                <button class="btn btn-outline-secondary" id="fitToContent" disabled>
                                    <i class="fas fa-expand-arrows-alt"></i> 适应画布
                                </button>
                                <button class="btn btn-outline-secondary" id="resetZoom" disabled>
                                    <i class="fas fa-search"></i> 重置缩放
                                </button>
                                <button class="btn btn-outline-info" id="toggleSidebar" disabled>
                                    <i class="fas fa-bars"></i> 场景列表
                                </button>
                                <button class="btn btn-outline-success" id="fullscreenBtn" disabled>
                                    <i class="fas fa-expand"></i> 全屏
                                </button>
                            </div>
                        </div>
                        <div class="panel-body">
                            <!-- 流程图画布容器 -->
                            <div class="flowchart-container">
                                <div id="x6-container"></div>

                                <!-- 左侧场景列表（可拖拽） -->
                                <div class="scenes-sidebar" id="scenesSidebar">
                                    <div class="scenes-sidebar-header">
                                        <h4>场景列表</h4>
                                        <button class="sidebar-toggle" id="sidebarToggle">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                    <div class="scenes-sidebar-body">
                                        <div id="draggableScenesList">
                                            <!-- 可拖拽的场景列表将通过JS动态生成 -->
                                        </div>
                                    </div>
                                </div>

                                <!-- 空状态提示 -->
                                <div class="no-position-selected" id="noPositionSelected">
                                    <div class="flowchart-empty">
                                        <div class="flowchart-empty-icon">
                                            <i class="fas fa-project-diagram"></i>
                                        </div>
                                        <div class="flowchart-empty-text">请从左侧选择一个职位分类</div>
                                        <div class="flowchart-empty-hint">选择后即可开始设计面试流程</div>
                                    </div>
                                </div>

                                <!-- 加载状态 -->
                                <div class="flowchart-loading" id="flowchartLoading" style="display: none;">
                                    <div class="spinner"></div>
                                    <div>加载中...</div>
                                </div>
                            </div>

                            <!-- 传统列表视图 -->
                            <div class="scenes-list-view" id="scenesListView" style="display: none;">
                                <div class="scenes-list" id="scenesList">
                                    <!-- 场景列表将通过JS动态生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加场景弹窗 -->
    <div class="modal" id="addSceneModal">
        <div class="modal-dialog" style="max-width: 1200px; width: 98%;">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">添加面试场景</h4>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="addSceneForm">
                        <input type="hidden" id="positionId" name="job_classification_id">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="sceneName" class="form-label">场景名称:</label>
                                    <input type="text" id="sceneName" name="sceneName" class="form-control" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="sceneDescription" class="form-label">场景描述:</label>
                                    <textarea id="sceneDescription" name="sceneDescription" class="form-control" rows="3"></textarea>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">分数范围:</label>
                                    <div class="score-range-inputs">
                                        <input type="number" id="scoreMin" name="scoreMin" class="form-control" min="0" placeholder="最小值" required>
                                        <span class="range-separator">至</span>
                                        <input type="number" id="scoreMax" name="scoreMax" class="form-control" min="0" placeholder="最大值" required>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="checkbox">
                                                <input type="checkbox" id="isDefault" name="isDefault">
                                                <label for="isDefault">设为默认</label>
                                            </div>
                                        </div>
                                        
                                        <div class="form-group">
                                            <div class="checkbox">
                                                <input type="checkbox" id="enableSemanticClassifier" name="enableSemanticClassifier">
                                                <label for="enableSemanticClassifier">启用语义分类器</label>
                                            </div>
                                        </div>
                                        
                                        <div class="form-group">
                                            <div class="checkbox">
                                                <input type="checkbox" id="enableAnswerClassifier" name="enableAnswerClassifier">
                                                <label for="enableAnswerClassifier">启用答案分类器</label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="checkbox">
                                                <input type="checkbox" id="allowedToFallBack" name="allowedToFallBack">
                                                <label for="allowedToFallBack">允许回退</label>
                                            </div>
                                        </div>
                                        
                                        <div class="form-group">
                                            <div class="checkbox">
                                                <input type="checkbox" id="enableScoreTrigger" name="enableScoreTrigger">
                                                <label for="enableScoreTrigger">启用分数触发器</label>
                                            </div>
                                        </div>
                                        
                                        <div class="form-group">
                                            <div class="checkbox">
                                                <input type="checkbox" id="enableSemanticTrigger" name="enableSemanticTrigger">
                                                <label for="enableSemanticTrigger">启用语意触发器</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="score_trigger_ids" class="form-label">分数触发器 (多选):</label>
                                    <select id="score_trigger_ids" name="score_trigger_ids" class="form-control select2" multiple="multiple">
                                        <!-- 触发器选项将通过JS动态添加 -->
                                    </select>
                                    <small>可选，当候选人分数达到指定值时触发相应动作</small>
                                </div>
                                
                                <div class="form-group">
                                    <label for="semantic_classifier_ids" class="form-label">语意分类器 (多选):</label>
                                    <select id="semantic_classifier_ids" name="semantic_classifier_ids" class="form-control select2" multiple="multiple">
                                        <!-- 分类器选项将通过JS动态添加 -->
                                    </select>
                                    <small>可选，选择要应用于此场景的语意分类器</small>
                                </div>
                                
                                <div class="form-group">
                                    <label for="semantic_trigger_ids" class="form-label">语意触发器 (多选):</label>
                                    <select id="semantic_trigger_ids" name="semantic_trigger_ids" class="form-control select2" multiple="multiple">
                                        <!-- 触发器选项将通过JS动态添加 -->
                                    </select>
                                    <small>可选，当候选人回答包含特定语意时触发相应动作</small>
                                </div>
                                
                                <div class="form-group">
                                    <label for="answer_classifier_id" class="form-label">答案分类器 (单选):</label>
                                    <select id="answer_classifier_id" name="answer_classifier_id" class="form-control">
                                        <option value="">不设置分类器</option>
                                        <!-- 分类器选项将通过JS动态添加 -->
                                    </select>
                                    <small>可选，用于判断候选人回答的质量</small>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveSceneBtn">确定</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加问题弹窗 -->
    <div class="modal" id="addQuestionModal">
        <div class="modal-dialog" style="max-width: 700px; width: 90%;">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">添加面试问题</h4>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="addQuestionForm">
                        <input type="hidden" id="questionSceneId" name="sceneId">
                        
                        <div class="form-group">
                            <label for="questionContent" class="form-label">问题内容:</label>
                            <textarea id="questionContent" name="questionContent" class="form-control" rows="5" required></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="questionScore" class="form-label">问题分值:</label>
                            <input type="number" id="questionScore" name="questionScore" class="form-control" min="0" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveQuestionBtn">确定</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑场景弹窗 -->
    <div class="modal" id="editSceneModal">
        <div class="modal-dialog" style="max-width: 1200px; width: 98%;">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">编辑面试场景</h4>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="editSceneForm">
                        <input type="hidden" id="editSceneId" name="sceneId">
                        <input type="hidden" id="editPositionId" name="job_classification_id">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="editSceneName" class="form-label">场景名称:</label>
                                    <input type="text" id="editSceneName" name="sceneName" class="form-control" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="editSceneDescription" class="form-label">场景描述:</label>
                                    <textarea id="editSceneDescription" name="sceneDescription" class="form-control" rows="3"></textarea>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">分数范围:</label>
                                    <div class="score-range-inputs">
                                        <input type="number" id="editScoreMin" name="scoreMin" class="form-control" min="0" placeholder="最小值" required>
                                        <span class="range-separator">至</span>
                                        <input type="number" id="editScoreMax" name="scoreMax" class="form-control" min="0" placeholder="最大值" required>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="checkbox">
                                                <input type="checkbox" id="editIsDefault" name="isDefault">
                                                <label for="editIsDefault">设为默认</label>
                                            </div>
                                        </div>
                                        
                                        <div class="form-group">
                                            <div class="checkbox">
                                                <input type="checkbox" id="editEnableSemanticClassifier" name="enableSemanticClassifier">
                                                <label for="editEnableSemanticClassifier">启用语义分类器</label>
                                            </div>
                                        </div>
                                        
                                        <div class="form-group">
                                            <div class="checkbox">
                                                <input type="checkbox" id="editEnableAnswerClassifier" name="enableAnswerClassifier">
                                                <label for="editEnableAnswerClassifier">启用答案分类器</label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="checkbox">
                                                <input type="checkbox" id="editAllowedToFallBack" name="allowedToFallBack">
                                                <label for="editAllowedToFallBack">允许回退</label>
                                            </div>
                                        </div>
                                        
                                        <div class="form-group">
                                            <div class="checkbox">
                                                <input type="checkbox" id="editEnableScoreTrigger" name="enableScoreTrigger">
                                                <label for="editEnableScoreTrigger">启用分数触发器</label>
                                            </div>
                                        </div>
                                        
                                        <div class="form-group">
                                            <div class="checkbox">
                                                <input type="checkbox" id="editEnableSemanticTrigger" name="enableSemanticTrigger">
                                                <label for="editEnableSemanticTrigger">启用语意触发器</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="editScoreTriggerIds" class="form-label">分数触发器 (多选):</label>
                                    <select id="editScoreTriggerIds" name="score_trigger_ids" class="form-control select2" multiple="multiple">
                                        <!-- 触发器选项将通过JS动态添加 -->
                                    </select>
                                    <small>可选，当候选人分数达到指定值时触发相应动作</small>
                                </div>
                                
                                <div class="form-group">
                                    <label for="editSemanticClassifierIds" class="form-label">语意分类器 (多选):</label>
                                    <select id="editSemanticClassifierIds" name="semantic_classifier_ids" class="form-control select2" multiple="multiple">
                                        <!-- 分类器选项将通过JS动态添加 -->
                                    </select>
                                    <small>可选，选择要应用于此场景的语意分类器</small>
                                </div>
                                
                                <div class="form-group">
                                    <label for="editSemanticTriggerIds" class="form-label">语意触发器 (多选):</label>
                                    <select id="editSemanticTriggerIds" name="semantic_trigger_ids" class="form-control select2" multiple="multiple">
                                        <!-- 触发器选项将通过JS动态添加 -->
                                    </select>
                                    <small>可选，当候选人回答包含特定语意时触发相应动作</small>
                                </div>
                                
                                <div class="form-group">
                                    <label for="editAnswerClassifierId" class="form-label">答案分类器 (单选):</label>
                                    <select id="editAnswerClassifierId" name="answer_classifier_id" class="form-control">
                                        <option value="">不设置分类器</option>
                                        <!-- 分类器选项将通过JS动态添加 -->
                                    </select>
                                    <small>可选，用于判断候选人回答的质量</small>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="updateSceneBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 场景切换器设置弹窗 -->
    <div class="modal" id="switcherModal" style="z-index: 10000;">
        <div class="modal-dialog" style="max-width: 600px; width: 90%; z-index: 10001;">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">场景切换器设置</h4>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="switcherForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">源场景:</label>
                                    <input type="text" id="sourceSceneName" class="form-control" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">目标场景:</label>
                                    <input type="text" id="targetSceneName" class="form-control" readonly>
                                </div>
                            </div>
                        </div>

                        <!-- 分数条件设置 -->
                        <div class="form-group">
                            <label class="form-label">分数条件:</label>
                            <div class="row">
                                <div class="col-md-4">
                                    <select name="symbol" id="scoreSymbol" class="form-control">
                                        <option value="">不设置分数条件</option>
                                        <option value=">">大于</option>
                                        <option value=">=">大于等于</option>
                                        <option value="<">小于</option>
                                        <option value="<=">小于等于</option>
                                        <option value="=">等于</option>
                                    </select>
                                </div>
                                <div class="col-md-8">
                                    <input type="number" name="score" id="scoreValue" class="form-control" placeholder="分数值（可选）">
                                </div>
                            </div>
                            <small class="form-text text-muted">设置分数条件后，当用户分数满足条件时会触发场景切换</small>
                        </div>

                        <!-- 语义条件设置 -->
                        <div class="form-group">
                            <label class="form-label">语义条件:</label>
                            <textarea name="semantic_condition" id="semanticCondition" class="form-control" rows="3" placeholder="请输入自然语言描述，如：回答完所有的问题（可选）"></textarea>
                            <small class="form-text text-muted">设置语义条件后，当用户回答满足语义条件时会触发场景切换</small>
                        </div>

                        <!-- 目标分数设置 -->
                        <div class="form-group">
                            <label class="form-label">切换后赋予分数:</label>
                            <input type="number" name="target_score" id="targetScore" class="form-control" value="0" placeholder="切换到目标场景后给用户的分数">
                        </div>

                        <!-- 激活话术设置 -->
                        <div class="form-group">
                            <label class="form-label">激活话术:</label>
                            <textarea name="user_message" id="userMessage" class="form-control" rows="3" placeholder="切换到目标场景后的激活话术，如：请你继续"></textarea>
                            <small class="form-text text-muted">场景切换后系统会自动发送这条消息给用户</small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveSwitcher">保存切换器</button>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/jquery-3.6.0.min.js"></script>
    <script src="/static/js/sortable.min.js"></script>
    <script src="/static/js/select2.min.js"></script>
    <!-- X6流程图库 -->
    <script src="/static/js/x6index.js"></script>
    <script src="/static/js/x6-selection-index.js"></script>
    <script src="/static/js/x6-scroller-index.js"></script>
    <script src="/static/js/api.js"></script>
    <script src="/static/js/flowchart.js"></script>
    <script src="/static/js/main.js"></script>
    <script src="/static/js/scenes.js"></script>
    <script src="/static/js/sweetalert2@11"></script>
    <script src="/static/js/user-logout.js"></script>
    <script src="/static/js/auth07082.js"></script>
    <script src="/static/js/auth-check.js"></script>
</body>
</html> 