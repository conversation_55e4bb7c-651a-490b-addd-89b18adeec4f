<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI招聘客服管理系统 - 渠道接入管理</title>
    <link rel="stylesheet" href="/static/css/all.min.css">
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/scenes.css">
    <link rel="stylesheet" href="/static/css/bootstrap.min.css">
    <style>
        /* 基础字体大小设置，确保与其他页面一致 */
        body {
            font-size: 14px;
        }
        
        
        /* 渠道容器布局，类似scenes-container，修改为三栏布局 */
        .channel-container {
            display: flex;
            gap: 20px;
            height: calc(100vh - 120px);
        }
        
        /* 左侧APIKey面板 */
        .apikey-panel {
            background-color: white;
            border-radius: 5px;
            box-shadow: var(--shadow);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            flex: 1;
            max-width: 250px;
        }
        
        /* 中间渠道面板 */
        .channels-panel {
            background-color: white;
            border-radius: 5px;
            box-shadow: var(--shadow);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            flex: 1;
            max-width: 250px;
        }
        
        /* 右侧配置面板 */
        .config-panel {
            background-color: white;
            border-radius: 5px;
            box-shadow: var(--shadow);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            flex: 3;
        }
        
        /* 面板头部 */
        .panel-header {
            padding: 15px;
            background-color: var(--bg-light);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .panel-header h3 {
            margin: 0;
            font-size: 16px;
        }
        
        /* 面板内容 */
        .panel-body {
            padding: 0;
            overflow-y: auto;
            flex: 1;
        }
        
        /* 渠道列表样式 */
        .channel-list, .apikey-list {
            display: flex;
            flex-direction: column;
        }
        
        .channel-item, .apikey-item {
            padding: 12px 15px;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            display: flex;
            align-items: center;
            transition: all 0.2s;
        }
        
        .channel-item:hover, .apikey-item:hover {
            background-color: var(--bg-light);
        }
        
        .channel-item.active, .apikey-item.active {
            background-color: var(--primary-color);
            color: white;
        }
        
        .channel-item img {
            width: 20px;
            height: 20px;
            margin-right: 10px;
        }
        
        .apikey-item i {
            width: 20px;
            margin-right: 10px;
            text-align: center;
        }
        
        /* 配置表单样式 */
        .channel-config {
            padding: 15px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            font-weight: 500;
            margin-bottom: 8px;
            display: block;
            font-size: 14px;
        }
        
        .form-switch {
            padding-left: 2.5em;
            margin-bottom: 10px;
        }
        
        .form-switch .form-check-input {
            width: 2.5em;
            margin-left: -2.5em;
        }
        
        .form-check-label {
            font-size: 14px;
        }
        
        .text-muted {
            font-size: 12px;
            color: var(--text-light);
        }
        
        /* 没有数据提示 */
        .no-data {
            padding: 20px;
            text-align: center;
            color: var(--text-light);
        }
        
        /* 响应式布局 */
        @media (max-width: 992px) {
            .channel-container {
                flex-direction: column;
                height: auto;
            }
            
            .apikey-panel, .channels-panel, .config-panel {
                max-width: 100%;
                margin-bottom: 20px;
            }
        }
        
        /* 通知样式 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 15px;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            z-index: 1050;
            transition: all 0.3s ease;
            opacity: 1;
            min-width: 250px;
            font-size: 13px;
        }
        
        .notification-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .notification-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .notification-hide {
            opacity: 0;
            transform: translateX(30px);
        }
    </style>
</head>
<body>
    <div class="app-wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>AI招聘客服管理系统</h2>
            </div>
            <div class="sidebar-menu">
                <div class="sidebar-menu-item" data-link="index">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表盘</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-briefcase"></i>
                    <span>职位管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="job_classification">
                        <i class="fas fa-tags"></i>
                        <span>职位分类</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="batch_management">
                        <i class="fas fa-layer-group"></i>
                        <span>批量管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="positions">
                        <i class="fas fa-list"></i>
                        <span>职位列表</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="candidates">
                    <i class="fas fa-user-tie"></i>
                    <span>候选人管理</span>
                </div>
                <div class="sidebar-menu-item" data-link="virtual_hr">
                    <i class="fas fa-robot"></i>
                    <span>虚拟HR</span>
                </div>
                <div class="sidebar-menu-item" data-link="scenes">
                    <i class="fas fa-tasks"></i>
                    <span>面试场景</span>
                </div>
                <div class="sidebar-menu-item" data-link="interview">
                    <i class="fas fa-comments"></i>
                    <span>面试互动（仅供测试）</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-star"></i>
                    <span>胜任力模型</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="triggers">
                        <i class="fas fa-bolt"></i>
                        <span>触发器</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="classifier">
                        <i class="fas fa-filter"></i>
                        <span>分类器</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="ai_brain">
                    <i class="fas fa-brain"></i>
                    <span>AI大脑</span>
                </div>
                <div class="sidebar-menu-item has-submenu open">
                    <i class="fas fa-chart-line"></i>
                    <span>AI数据透视</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu show">
                    <div class="sidebar-menu-item active" data-link="scheduled_analysis">
                        <i class="fas fa-clock"></i>
                        <span>客情分析</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="qa_engine">
                        <i class="fas fa-question-circle"></i>
                        <span>QA引擎</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-key"></i>
                    <span>APIKey管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="apikey_create">
                        <i class="fas fa-plus"></i>
                        <span>创建APIKey</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="apikey_usage">
                        <i class="fas fa-chart-bar"></i>
                        <span>用量统计</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu active">
                    <i class="fas fa-cog"></i>
                    <span>用户设置</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu show">
                <div class="sidebar-menu-item active" data-link="channel_management">
                    <i class="fas fa-plug"></i>
                    <span>渠道接入管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="global_settings">
                        <i class="fas fa-sliders-h"></i>
                        <span>全局配置</span>
                    </div>
                </div>
                <div class="sidebar-menu-item admin-only" data-link="user_management07082">
                    <i class="fas fa-user-cog"></i>
                    <span>用户管理</span>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部导航栏 -->
            <div class="topbar">
                <div class="topbar-toggle">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="topbar-title">
                    渠道接入管理
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <div class="channel-container">
                    <!-- 左侧APIKey列表 -->
                    <div class="apikey-panel">
                        <div class="panel-header">
                            <h3>可用的APIKey</h3>
                        </div>
                        <div class="panel-body">
                            <div class="apikey-list" id="apikeyList">
                                <!-- APIKey列表将通过JavaScript动态加载 -->
                                <div class="no-data" id="noApikey">
                                    <i class="fas fa-info-circle"></i> 暂无可用APIKey
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 中间渠道列表 -->
                    <div class="channels-panel">
                        <div class="panel-header">
                            <h3>支持的渠道</h3>
                        </div>
                        <div class="panel-body">
                            <div class="channel-list" id="channelList">
                                <!-- 渠道列表将通过JavaScript动态加载 -->
                                <div class="no-data" id="noChannel">
                                    <i class="fas fa-info-circle"></i> 请先选择APIKey
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 右侧配置面板 -->
                    <div class="config-panel">
                        <div class="panel-header">
                            <h3 id="currentChannelName">渠道配置</h3>
                        </div>
                        <div class="panel-body">
                            <div class="channel-config">
                                <form id="channelForm">
                                    <div class="no-data" id="noConfig">
                                        <i class="fas fa-info-circle"></i> 请先选择APIKey和渠道
                                    </div>
                                    <div id="configForm" style="display: none;">
                                        <div class="form-group">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="enablePreScenario">
                                                <label class="form-check-label" for="enablePreScenario">是否启用前置场景</label>
                                            </div>
                                            <small class="text-muted">启用前置场景后，退出前置场景才会进行信息提取创建用户，适用于社交APP渠道来源，招聘渠道不必开启</small>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="preScenarioPrompt" class="form-label">前置场景提示词</label>
                                            <textarea class="form-control" id="preScenarioPrompt" rows="8" placeholder="请输入前置场景提示词"></textarea>
                                            <small class="text-muted">前置场景提示词主要在用户进入场景前，获取用户信息，从而为结束前置场景时创建或匹配用户做准备</small>
                                        </div>
                                        
                                        <div class="form-group">
                                            <button type="button" class="btn btn-primary" id="saveChannelConfig">
                                                <i class="fas fa-save"></i> 保存
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/jquery-3.6.0.min.js"></script>
    <script src="/static/js/main.js"></script>
    <script src="/static/js/auth07082.js"></script>
    <script src="/static/js/auth-check.js"></script>
    <script src="/static/js/user-logout.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            let currentApikey = null; // 当前选中的APIKey
            let currentChannel = null; // 当前选中的渠道
            
            // 初始化页面
            const apikeyList = document.getElementById('apikeyList');
            const channelList = document.getElementById('channelList');
            const enablePreScenario = document.getElementById('enablePreScenario');
            const preScenarioPrompt = document.getElementById('preScenarioPrompt');
            const saveButton = document.getElementById('saveChannelConfig');
            const currentChannelName = document.getElementById('currentChannelName');
            const noApikey = document.getElementById('noApikey');
            const noChannel = document.getElementById('noChannel');
            const noConfig = document.getElementById('noConfig');
            const configForm = document.getElementById('configForm');
            
            // 加载APIKey列表
            loadApikeys();
            
            // 加载APIKey列表
            function loadApikeys() {
                // 清空现有列表
                apikeyList.innerHTML = '';
                
                // 显示加载状态
                apikeyList.innerHTML = '<div class="no-data"><i class="fas fa-spinner fa-spin"></i> 加载中...</div>';
                
                // 使用JWT认证请求
                authenticatedFetch('/api/apikeys')
                    .then(response => response.json())
                    .then(data => {
                        if (data.code === 200 && data.data && data.data.length > 0) {
                            // 清空并填充APIKey列表
                            apikeyList.innerHTML = '';
                            
                            data.data.forEach(apikey => {
                                const apikeyItem = document.createElement('div');
                                apikeyItem.className = 'apikey-item';
                                apikeyItem.setAttribute('data-apikey', apikey.apikey);
                                apikeyItem.innerHTML = `
                                    <i class="fas fa-key"></i>
                                    <span>${apikey.name}</span>
                                `;
                                apikeyList.appendChild(apikeyItem);
                                
                                // 添加点击事件
                                apikeyItem.addEventListener('click', function() {
                                    // 更新当前选中的APIKey
                                    currentApikey = this.getAttribute('data-apikey');
                                    
                                    // 更新UI选中状态
                                    document.querySelectorAll('.apikey-item').forEach(item => {
                                        item.classList.remove('active');
                                    });
                                    this.classList.add('active');
                                    
                                    // 重置渠道选择和配置
                                    currentChannel = null;
                                    currentChannelName.textContent = '渠道配置';
                                    document.querySelectorAll('.channel-item').forEach(item => {
                                        item.classList.remove('active');
                                    });
                                    configForm.style.display = 'none';
                                    noConfig.style.display = 'block';
                                    
                                    // 加载该APIKey下的渠道
                                    loadChannels(currentApikey);
                                });
                            });
                            
                            // 隐藏无数据提示
                            noApikey.style.display = 'none';
                        } else {
                            // 显示无数据提示
                            apikeyList.innerHTML = '';
                            apikeyList.appendChild(noApikey);
                            noApikey.style.display = 'block';
                        }
                    })
                    .catch(error => {
                        console.error('获取APIKey列表失败:', error);
                        apikeyList.innerHTML = '<div class="no-data"><i class="fas fa-exclamation-circle"></i> 加载失败</div>';
                    });
            }
            
            // 加载渠道列表
            function loadChannels(apikey) {
                // 清空现有列表
                channelList.innerHTML = '';
                
                // 显示加载状态
                channelList.innerHTML = '<div class="no-data"><i class="fas fa-spinner fa-spin"></i> 加载中...</div>';
                
                // 默认显示的渠道
                const defaultChannels = [
                    { type: 'boss', name: 'BOSS直聘', icon: '/static/img/icons/boss.png' },
                    { type: 'wechat', name: '微信', icon: '/static/img/icons/wechat.png' }
                ];
                
                // 清空并填充渠道列表
                channelList.innerHTML = '';
                
                defaultChannels.forEach(channel => {
                    const channelItem = document.createElement('div');
                    channelItem.className = 'channel-item';
                    channelItem.setAttribute('data-channel', channel.type);
                    channelItem.innerHTML = `
                        <img src="${channel.icon}" alt="${channel.name}">
                        <span>${channel.name}</span>
                    `;
                    channelList.appendChild(channelItem);
                    
                    // 添加点击事件
                    channelItem.addEventListener('click', function() {
                        const channel = this.getAttribute('data-channel');
                        currentChannel = channel;
                        
                        // 更新UI选中状态
                        document.querySelectorAll('.channel-item').forEach(item => {
                            item.classList.remove('active');
                        });
                        this.classList.add('active');
                        
                        // 更新渠道名称
                        currentChannelName.textContent = this.querySelector('span').textContent;
                        
                        // 加载渠道配置
                        loadChannelConfig(currentApikey, channel);
                    });
                });
                
                // 隐藏无数据提示
                noChannel.style.display = 'none';
            }
            
            // 保存渠道配置
            saveButton.addEventListener('click', function() {
                if (!currentApikey || !currentChannel) {
                    showNotification('请先选择APIKey和渠道', 'error');
                    return;
                }
                
                saveChannelConfig(currentApikey, currentChannel);
            });
            
            // 加载渠道配置
            function loadChannelConfig(apikey, channel) {
                if (!apikey || !channel) {
                    configForm.style.display = 'none';
                    noConfig.style.display = 'block';
                    return;
                }
                
                // 显示加载状态
                configForm.style.display = 'none';
                noConfig.style.display = 'block';
                noConfig.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 加载中...';
                
                // 使用JWT认证请求
                authenticatedFetch(`/api/channel?apikey=${apikey}&channel=${channel}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.code === 200 && data.data) {
                            // 填充表单数据
                            enablePreScenario.checked = data.data.enable_pre_scenario === 1;
                            preScenarioPrompt.value = data.data.pre_scenario_prompt || '';
                            
                            // 显示表单
                            configForm.style.display = 'block';
                            noConfig.style.display = 'none';
                        } else {
                            // 渠道未配置，设置默认值
                            enablePreScenario.checked = false;
                            preScenarioPrompt.value = '';
                            
                            // 显示表单
                            configForm.style.display = 'block';
                            noConfig.style.display = 'none';
                        }
                    })
                    .catch(error => {
                        console.error('获取渠道配置失败:', error);
                        noConfig.innerHTML = '<i class="fas fa-exclamation-circle"></i> 加载失败';
                        showNotification('获取渠道配置失败，请重试', 'error');
                    });
            }
            
            // 保存渠道配置
            function saveChannelConfig(apikey, channel) {
                const isEnabled = enablePreScenario.checked ? 1 : 0;
                const prompt = preScenarioPrompt.value.trim();
                
                const data = {
                    apikey: apikey,
                    channel_type: channel,
                    enable_pre_scenario: isEnabled,
                    pre_scenario_prompt: prompt
                };
                
                // 显示加载状态
                saveButton.disabled = true;
                saveButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...';
                
                // 使用JWT认证请求
                authenticatedFetch('/api/channel', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.code === 200) {
                            showNotification('保存成功', 'success');
                        } else {
                            showNotification(`保存失败: ${data.message}`, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('保存渠道配置失败:', error);
                        showNotification('保存失败，请稍后重试', 'error');
                    })
                    .finally(() => {
                        // 恢复按钮状态
                        saveButton.disabled = false;
                        saveButton.innerHTML = '<i class="fas fa-save"></i> 保存';
                    });
            }
            
            // 显示通知
            function showNotification(message, type) {
                const notificationContainer = document.createElement('div');
                notificationContainer.className = `notification notification-${type}`;
                notificationContainer.innerHTML = message;
                document.body.appendChild(notificationContainer);
                
                setTimeout(() => {
                    notificationContainer.classList.add('notification-hide');
                    setTimeout(() => {
                        notificationContainer.remove();
                    }, 300);
                }, 3000);
            }
        });
    </script>
</body>
</html>
