<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI招聘客服管理系统 - APIKey管理</title>
    <link rel="stylesheet" href="/static/css/all.min.css">
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/apikey.css">
    <script src="/static/js/auth07082.js"></script>
    <script src="/static/js/auth-check.js"></script>
</head>
<body>
    <div class="app-wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>AI招聘客服管理系统</h2>
            </div>
            <div class="sidebar-menu">
                <div class="sidebar-menu-item" data-link="index">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表盘</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-briefcase"></i>
                    <span>职位管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="job_classification">
                        <i class="fas fa-tags"></i>
                        <span>职位分类</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="batch_management">
                        <i class="fas fa-layer-group"></i>
                        <span>批量管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="positions">
                        <i class="fas fa-list"></i>
                        <span>职位列表</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="candidates">
                    <i class="fas fa-user-tie"></i>
                    <span>候选人管理</span>
                </div>
                <div class="sidebar-menu-item" data-link="virtual_hr">
                    <i class="fas fa-robot"></i>
                    <span>虚拟HR</span>
                </div>
                <div class="sidebar-menu-item" data-link="scenes">
                    <i class="fas fa-tasks"></i>
                    <span>面试场景</span>
                </div>
                <div class="sidebar-menu-item" data-link="interview">
                    <i class="fas fa-comments"></i>
                    <span>面试互动（仅供测试）</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-star"></i>
                    <span>胜任力模型</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="triggers">
                        <i class="fas fa-bolt"></i>
                        <span>触发器</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="classifier">
                        <i class="fas fa-filter"></i>
                        <span>分类器</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="ai_brain">
                    <i class="fas fa-brain"></i>
                    <span>AI大脑</span>
                </div>
                <div class="sidebar-menu-item has-submenu open">
                    <i class="fas fa-chart-line"></i>
                    <span>AI数据透视</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu show">
                    <div class="sidebar-menu-item active" data-link="scheduled_analysis">
                        <i class="fas fa-clock"></i>
                        <span>客情分析</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="qa_engine">
                        <i class="fas fa-question-circle"></i>
                        <span>QA引擎</span>
                    </div>
                </div>
                <div class="sidebar-menu-item active has-submenu">
                    <i class="fas fa-key"></i>
                    <span>APIKey管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu show">
                    <div class="sidebar-menu-item active" data-link="apikey_create">
                        <i class="fas fa-plus"></i>
                        <span>创建APIKey</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="apikey_usage">
                        <i class="fas fa-chart-bar"></i>
                        <span>用量统计</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-cog"></i>
                    <span>用户设置</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="channel_management">
                        <i class="fas fa-plug"></i>
                        <span>渠道接入管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="global_settings">
                        <i class="fas fa-sliders-h"></i>
                        <span>全局配置</span>
                    </div>
                </div>
                <div class="sidebar-menu-item admin-only" data-link="user_management07082">
                    <i class="fas fa-user-cog"></i>
                    <span>用户管理</span>
                </div>
            </div>
        </div>

        <div class="main-content">
            <!-- 顶部导航栏 -->
            <div class="topbar">
                <div class="topbar-toggle">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="topbar-title">
                    APIKey管理
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <div class="section-header">
                    <h2>创建和管理APIKey</h2>
                    <p>APIKey用于调用系统API，请妥善保管您的密钥</p>
                </div>

                <!-- 创建APIKey表单 -->
                <div class="card">
                    <div class="card-header">
                        <h3>创建新的APIKey</h3>
                    </div>
                    <div class="card-body">
                        <form id="createApikeyForm" class="form">
                            <div class="form-group">
                                <label for="apiKeyName">APIKey名称</label>
                                <input type="text" id="apiKeyName" name="apiKeyName" placeholder="输入一个易于识别的名称..." required>
                                <div class="form-help">为您的APIKey添加一个描述性名称，方便后续识别</div>
                            </div>
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">创建APIKey</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- APIKey列表 -->
                <div class="card mt-20">
                    <div class="card-header">
                        <h3>您的APIKey列表</h3>
                    </div>
                    <div class="card-body">
                        <div id="apiKeyListLoading" class="loading-indicator">
                            <i class="fas fa-spinner fa-spin"></i> 加载中...
                        </div>
                        <div id="apiKeyListEmpty" class="empty-message" style="display: none;">
                            <i class="fas fa-info-circle"></i> 您还没有创建任何APIKey
                        </div>
                        <table id="apiKeyTable" class="data-table" style="display: none;">
                            <thead>
                                <tr>
                                    <th>名称</th>
                                    <th>APIKey</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="apiKeyList">
                                <!-- APIKey列表将通过JS动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 使用说明 -->
                <div class="card mt-20">
                    <div class="card-header">
                        <h3>APIKey使用说明</h3>
                    </div>
                    <div class="card-body">
                        <div class="api-usage-guide">
                            <h4>如何使用APIKey</h4>
                            <p>您可以通过以下方式在API请求中使用您的APIKey：</p>
                            
                            <div class="code-block">
                                <pre><code>POST /chat
Content-Type: application/json
Authorization: Bearer 您的APIKey

{
    "messages": [
        {"role": "user", "content": "您的问题"}
    ]
}</code></pre>
                            </div>
                            
                            <h4>注意事项</h4>
                            <ul>
                                <li>请勿在公开的代码中直接使用APIKey</li>
                                <li>如果您怀疑APIKey泄露，请立即删除并创建新的</li>
                                <li>系统会记录每个APIKey的使用情况和Token消耗</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/api.js"></script>
    <script src="/static/js/main.js"></script>
    <script src="/static/js/apikey.js"></script>
    <script src="/static/js/user-logout.js"></script>
</body>
</html> 