<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI招聘客服管理系统 - 职位管理</title>
    <link rel="stylesheet" href="/static/css/all.min.css">
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/positions.css">
</head>
<body>
    <div class="app-wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>AI招聘客服管理系统</h2>
            </div>
            <div class="sidebar-menu">
                <div class="sidebar-menu-item" data-link="index">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表盘</span>
                </div>
                <div class="sidebar-menu-item active has-submenu">
                    <i class="fas fa-briefcase"></i>
                    <span>职位管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu show">
                    <div class="sidebar-menu-item" data-link="job_classification">
                        <i class="fas fa-tags"></i>
                        <span>职位分类</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="batch_management">
                        <i class="fas fa-layer-group"></i>
                        <span>批量管理</span>
                    </div>
                    <div class="sidebar-menu-item active" data-link="positions">
                        <i class="fas fa-list"></i>
                        <span>职位列表</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="candidates">
                    <i class="fas fa-user-tie"></i>
                    <span>候选人管理</span>
                </div>
                <div class="sidebar-menu-item" data-link="virtual_hr">
                    <i class="fas fa-robot"></i>
                    <span>虚拟HR</span>
                </div>
                <div class="sidebar-menu-item" data-link="scenes">
                    <i class="fas fa-tasks"></i>
                    <span>面试场景</span>
                </div>
                <div class="sidebar-menu-item" data-link="interview">
                    <i class="fas fa-comments"></i>
                    <span>面试互动（仅供测试）</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-star"></i>
                    <span>胜任力模型</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="triggers">
                        <i class="fas fa-bolt"></i>
                        <span>触发器</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="classifier">
                        <i class="fas fa-filter"></i>
                        <span>分类器</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="ai_brain">
                    <i class="fas fa-brain"></i>
                    <span>AI大脑</span>
                </div>
                <div class="sidebar-menu-item has-submenu open">
                    <i class="fas fa-chart-line"></i>
                    <span>AI数据透视</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu show">
                    <div class="sidebar-menu-item active" data-link="scheduled_analysis">
                        <i class="fas fa-clock"></i>
                        <span>客情分析</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="qa_engine">
                        <i class="fas fa-question-circle"></i>
                        <span>QA引擎</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-key"></i>
                    <span>APIKey管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="apikey_create">
                        <i class="fas fa-plus"></i>
                        <span>创建APIKey</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="apikey_usage">
                        <i class="fas fa-chart-bar"></i>
                        <span>用量统计</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-cog"></i>
                    <span>用户设置</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="channel_management">
                        <i class="fas fa-plug"></i>
                        <span>渠道接入管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="global_settings">
                        <i class="fas fa-sliders-h"></i>
                        <span>全局配置</span>
                    </div>
                </div>
                <div class="sidebar-menu-item admin-only" data-link="user_management07082">
                    <i class="fas fa-user-cog"></i>
                    <span>用户管理</span>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部导航栏 -->
            <div class="topbar">
                <div class="topbar-toggle">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="topbar-title">
                    职位管理
                </div>
                
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 页面标题和操作 -->
                <div class="page-header">
                    <h2>职位列表</h2>
                    <button class="btn btn-primary" id="addPositionBtn">
                        <i class="fas fa-plus"></i> 新增职位
                    </button>
                </div>

                <!-- 搜索和筛选 -->
                <div class="card mb-20">
                    <div class="card-body">
                        <div class="filter-container">
                            <div class="search-box">
                                <input type="text" id="searchInput" class="form-control" placeholder="搜索职位名称、城市...">
                                <button class="btn btn-primary" id="searchBtn">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                            </div>
                            <div class="filter-options">
                                <div class="form-group">
                                    <label for="cityFilter" class="form-label">城市:</label>
                                    <select id="cityFilter" class="form-control">
                                        <option value="">全部</option>
                                        <!-- 城市选项将通过JS动态添加 -->
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="sortBy" class="form-label">排序方式:</label>
                                    <select id="sortBy" class="form-control">
                                        <option value="newest">最新添加</option>
                                        <option value="name">职位名称</option>
                                        <option value="city">城市</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 职位列表 -->
                <div class="card">
                    <div class="card-body">
                        <div class="position-list" id="positionList">
                            <!-- 职位列表将通过JS动态生成 -->
                            <div class="position-list-loading">
                                <p>加载中...</p>
                            </div>
                        </div>
                        <!-- 分页 -->
                        <div class="pagination-container" id="pagination">
                            <!-- 分页将通过JS动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增/编辑职位模态框 -->
    <div class="modal" id="positionModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">新增职位</h3>
                <span class="modal-close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="positionForm" data-validate="true">
                    <input type="hidden" id="positionId" name="dataId">
                    
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label for="positionName" class="form-label">职位名称:</label>
                                <input type="text" id="positionName" name="positionName" class="form-control" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="jobClassificationId" class="form-label">职位分类:</label>
                                <select id="jobClassificationId" name="job_classification_id" class="form-control">
                                    <option value="">请选择职位分类</option>
                                    <!-- 职位分类选项将通过JS动态添加 -->
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="jobCity" class="form-label">工作城市:</label>
                                <input type="text" id="jobCity" name="jobCity" class="form-control" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="ageRange" class="form-label">年龄范围:</label>
                                <div class="range-inputs">
                                    <input type="number" id="ageMin" name="ageMin" class="form-control" placeholder="最小">
                                    <span>-</span>
                                    <input type="number" id="ageMax" name="ageMax" class="form-control" placeholder="最大">
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="gender" class="form-label">性别要求:</label>
                                <select id="gender" name="gender" class="form-control">
                                    <option value="">不限</option>
                                    <option value="男">男</option>
                                    <option value="女">女</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-col">
                            <div class="form-group">
                                <label for="educationRequirements" class="form-label">学历要求（按住Ctrl键可多选）:</label>
                                <select id="educationRequirements" name="educationRequirements" class="form-control" multiple>
                                    <option value="不限">不限</option>
                                    <option value="高中">高中</option>
                                    <option value="专科">专科</option>
                                    <option value="本科">本科</option>
                                    <option value="硕士">硕士</option>
                                    <option value="博士">博士</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="salaryRange" class="form-label">薪资范围:</label>
                                <input type="text" id="salaryRange" name="salaryRange" class="form-control">
                            </div>
                            
                            <div class="form-group">
                                <label for="boss_account" class="form-label">Boss账号:</label>
                                <input type="text" id="boss_account" name="boss_account" class="form-control">
                            </div>
                            
                            <div class="form-group">
                                <label for="device_info" class="form-label">设备信息:</label>
                                <input type="text" id="device_info" name="device_info" class="form-control">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-col" style="flex: 1;">
                            <div class="form-group">
                                <label for="boss_scene" class="form-label">Boss场景图片:</label>
                                <input type="text" id="boss_scene" name="boss_scene" class="form-control">
                            </div>
                        </div>
                        <div class="form-col" style="flex: 1;">
                            <div class="form-group">
                                <label for="enable_boss_scene" class="form-label">是否启动Boss场景图片:</label>
                                <select id="enable_boss_scene" name="enable_boss_scene" class="form-control">
                                    <option value="0">否</option>
                                    <option value="1">是</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="experienceRequirements" class="form-label">岗位描述或要求:</label>
                        <textarea id="experienceRequirements" name="experienceRequirements" class="form-control" rows="4" style="resize: vertical;"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-light" id="cancelBtn">取消</button>
                <button class="btn btn-primary" id="savePositionBtn">保存</button>
            </div>
        </div>
    </div>

    <!-- 职位详情模态框 -->
    <div class="modal" id="positionDetailModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="detailModalTitle">职位详情</h3>
                <span class="modal-close">&times;</span>
            </div>
            <div class="modal-body">
                <div id="positionDetails">
                    <!-- 职位详情将通过JS动态生成 -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-light" id="closeDetailBtn">关闭</button>
                <button class="btn btn-primary" id="editPositionBtn">编辑</button>
            </div>
        </div>
    </div>

    <!-- 脚本引用 -->
    <script src="/static/js/main.js"></script>
    <script src="/static/js/api.js"></script>
    <script src="/static/js/positions.js"></script>
    <script src="/static/js/sweetalert2@11"></script>
    <script src="/static/js/auth07082.js"></script>
    <script src="/static/js/auth-check.js"></script>
    <script src="/static/js/user-logout.js"></script>
</body>
</html> 