<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI招聘客服管理系统 - APIKey用量统计</title>
    <link rel="stylesheet" href="/static/css/all.min.css">
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/apikey.css">
    <script src="/static/js/auth07082.js"></script>
    <script src="/static/js/auth-check.js"></script>
    <script src="/static/js/chart.js"></script>
</head>
<body>
    <div class="app-wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>AI招聘客服管理系统</h2>
            </div>
            <div class="sidebar-menu">
                <div class="sidebar-menu-item" data-link="index">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表盘</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-briefcase"></i>
                    <span>职位管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="job_classification">
                        <i class="fas fa-tags"></i>
                        <span>职位分类</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="batch_management">
                        <i class="fas fa-layer-group"></i>
                        <span>批量管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="positions">
                        <i class="fas fa-list"></i>
                        <span>职位列表</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="candidates">
                    <i class="fas fa-user-tie"></i>
                    <span>候选人管理</span>
                </div>
                <div class="sidebar-menu-item" data-link="virtual_hr">
                    <i class="fas fa-robot"></i>
                    <span>虚拟HR</span>
                </div>
                <div class="sidebar-menu-item" data-link="scenes">
                    <i class="fas fa-tasks"></i>
                    <span>面试场景</span>
                </div>
                <div class="sidebar-menu-item" data-link="interview">
                    <i class="fas fa-comments"></i>
                    <span>面试互动（仅供测试）</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-star"></i>
                    <span>胜任力模型</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="triggers">
                        <i class="fas fa-bolt"></i>
                        <span>触发器</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="classifier">
                        <i class="fas fa-filter"></i>
                        <span>分类器</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="ai_brain">
                    <i class="fas fa-brain"></i>
                    <span>AI大脑</span>
                </div>
                <div class="sidebar-menu-item has-submenu open">
                    <i class="fas fa-chart-line"></i>
                    <span>AI数据透视</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu show">
                    <div class="sidebar-menu-item active" data-link="scheduled_analysis">
                        <i class="fas fa-clock"></i>
                        <span>客情分析</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="qa_engine">
                        <i class="fas fa-question-circle"></i>
                        <span>QA引擎</span>
                    </div>
                </div>
                <div class="sidebar-menu-item active has-submenu">
                    <i class="fas fa-key"></i>
                    <span>APIKey管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu show">
                    <div class="sidebar-menu-item" data-link="apikey_create">
                        <i class="fas fa-plus"></i>
                        <span>创建APIKey</span>
                    </div>
                    <div class="sidebar-menu-item active" data-link="apikey_usage">
                        <i class="fas fa-chart-bar"></i>
                        <span>用量统计</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-cog"></i>
                    <span>用户设置</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="channel_management">
                        <i class="fas fa-plug"></i>
                        <span>渠道接入管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="global_settings">
                        <i class="fas fa-sliders-h"></i>
                        <span>全局配置</span>
                    </div>
                </div>
                <div class="sidebar-menu-item admin-only" data-link="user_management07082">
                    <i class="fas fa-user-cog"></i>
                    <span>用户管理</span>
                </div>
            </div>
        </div>

        <div class="main-content">
            <!-- 顶部导航栏 -->
            <div class="topbar">
                <div class="topbar-toggle">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="topbar-title">
                    APIKey用量统计
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <div class="section-header">
                    <h2>API使用情况</h2>
                    <p>查看您的API使用情况和Token消耗</p>
                </div>
                
                <!-- 日期筛选 -->
                <div class="card">
                    <div class="card-header">
                        <h3>选择日期范围</h3>
                    </div>
                    <div class="card-body">
                        <form id="dateFilterForm" class="form-inline">
                            <div class="form-group">
                                <label for="startDate">开始日期</label>
                                <input type="date" id="startDate" name="startDate">
                            </div>
                            <div class="form-group">
                                <label for="endDate">结束日期</label>
                                <input type="date" id="endDate" name="endDate">
                            </div>
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">筛选</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 总体用量统计 -->
                <div class="card mt-20">
                    <div class="card-header">
                        <h3>总体用量统计</h3>
                    </div>
                    <div class="card-body">
                        <div id="usageSummaryLoading" class="loading-indicator">
                            <i class="fas fa-spinner fa-spin"></i> 加载中...
                        </div>
                        <div class="usage-summary" id="usageSummary" style="display: none;">
                            <div class="usage-stat">
                                <div class="stat-icon">
                                    <i class="fas fa-arrow-up"></i>
                                </div>
                                <div class="stat-info">
                                    <h4 id="totalUpTokens">0</h4>
                                    <p>输入Token</p>
                                </div>
                            </div>
                            <div class="usage-stat">
                                <div class="stat-icon">
                                    <i class="fas fa-arrow-down"></i>
                                </div>
                                <div class="stat-info">
                                    <h4 id="totalDownTokens">0</h4>
                                    <p>输出Token</p>
                                </div>
                            </div>
                            <div class="usage-stat">
                                <div class="stat-icon">
                                    <i class="fas fa-calculator"></i>
                                </div>
                                <div class="stat-info">
                                    <h4 id="totalTokens">0</h4>
                                    <p>总Token</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 按APIKey的用量统计 -->
                <div class="card mt-20">
                    <div class="card-header">
                        <h3>按APIKey的用量统计</h3>
                    </div>
                    <div class="card-body">
                        <div id="apikeyUsageLoading" class="loading-indicator">
                            <i class="fas fa-spinner fa-spin"></i> 加载中...
                        </div>
                        <div id="apikeyUsageEmpty" class="empty-message" style="display: none;">
                            <i class="fas fa-info-circle"></i> 所选时间段内没有API使用记录
                        </div>
                        <table id="apikeyUsageTable" class="data-table" style="display: none;">
                            <thead>
                                <tr>
                                    <th>APIKey名称</th>
                                    <th>输入Token</th>
                                    <th>输出Token</th>
                                    <th>总Token</th>
                                </tr>
                            </thead>
                            <tbody id="apikeyUsageList">
                                <!-- APIKey用量列表将通过JS动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 每日用量图表 -->
                <div class="card mt-20">
                    <div class="card-header">
                        <h3>每日用量趋势</h3>
                    </div>
                    <div class="card-body">
                        <div id="dailyUsageLoading" class="loading-indicator">
                            <i class="fas fa-spinner fa-spin"></i> 加载中...
                        </div>
                        <div id="dailyUsageEmpty" class="empty-message" style="display: none;">
                            <i class="fas fa-info-circle"></i> 所选时间段内没有API使用记录
                        </div>
                        <div id="dailyUsageChart" style="display: none;">
                            <canvas id="usageChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 分时用量图表 -->
                <div class="card mt-20">
                    <div class="card-header">
                        <h3>分时用量统计</h3>
                    </div>
                    <div class="card-body">
                        <form id="hourlyUsageForm" class="form-inline">
                            <div class="form-group">
                                <label for="apikeySelect">APIKey</label>
                                <select id="apikeySelect" name="apikeyId" class="form-control">
                                    <option value="">请选择APIKey</option>
                                    <!-- APIKey选项将通过JS动态生成 -->
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="hourlyStartDate">日期</label>
                                <input type="date" id="hourlyStartDate" name="hourlyStartDate" class="form-control">
                            </div>
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">查询</button>
                            </div>
                        </form>
                        
                        <div id="hourlyUsageLoading" class="loading-indicator" style="display: none;">
                            <i class="fas fa-spinner fa-spin"></i> 加载中...
                        </div>
                        <div id="hourlyUsageEmpty" class="empty-message" style="display: none;">
                            <i class="fas fa-info-circle"></i> 所选条件下没有分时API使用记录
                        </div>
                        <div id="hourlyUsageChart" style="display: none;">
                            <canvas id="hourlyChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/api.js"></script>
    <script src="/static/js/main.js"></script>
    <script src="/static/js/apikey-usage.js"></script>
    <script src="/static/js/user-logout.js"></script>
</body>
</html> 