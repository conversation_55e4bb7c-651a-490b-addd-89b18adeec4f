<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI招聘客服管理系统 - 分类器管理</title>
    <link rel="stylesheet" href="/static/css/all.min.css">
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/classifier.css">
    <style>
        /* 额外的样式 */
        /* 清除可能来自外部的overflow规则 */
        * {
            scrollbar-width: none !important; /* Firefox */
        }
        *::-webkit-scrollbar {
            display: none !important; /* Chrome, Safari, Edge */
        }
        
        .search-container {
            margin-bottom: 15px;
            display: flex;
        }
        .search-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .pagination-container {
            margin-top: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .pagination-info {
            font-size: 14px;
            color: #666;
        }
        .pagination-current {
            margin: 0 8px;
        }
        .empty-row {
            height: 42px;
            border-bottom: 1px solid #f3f3f3;
        }
        .table-container {
            min-height: 420px; /* 10条记录的大约高度 */
            overflow: visible !important; /* 强制保持可见，不显示滚动条 */
            width: 100%;
        }
        .prompt-card {
            margin-top: 15px;
            display: flex;
            flex-direction: column;
            height: auto;
            min-height: 300px;
        }
        .prompt-textarea {
            width: 100%;
            min-height: 150px;
            resize: vertical;
            padding: 10px;
            font-size: 14px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .card-footer {
            padding: 12px 15px;
            border-top: 1px solid #eaeaea;
            display: flex;
            justify-content: flex-end;
        }
        .classifier-panels {
            display: flex;
            gap: 20px;
        }
        .panel-column {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        .card-body {
            overflow: visible !important; /* 强制可见，防止滚动条 */
            padding: 15px;
            display: flex;
            flex-direction: column;
        }
        .prompt-card .card-body {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 15px;
            overflow: visible !important; /* 强制可见，防止滚动条 */
        }
        /* 设置表格内容的最大宽度和溢出处理 */
        .table td {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        /* 鼠标悬停时显示完整内容 */
        .table td:hover {
            white-space: normal;
            word-break: break-word;
        }
        /* 模态框样式调整 */
        .modal-body {
            padding: 20px;
            max-height: 60vh;
            overflow-y: auto;
        }
        /* 添加样式以支持每页显示条数选择器 */
        .form-select-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
            border-radius: 0.2rem;
            height: calc(1.5em + 0.5rem + 2px);
        }
        .items-per-page {
            margin-left: 8px;
            min-width: 80px;
            border: 1px solid #ddd;
        }
        /* 移除所有卡片内的滚动 */
        .card, .card-body, .card-header, .card-footer, .table-container, .table, 
        .pagination-container, .search-container, .prompt-card, .classifier-panels,
        .panel-column {
            overflow: visible !important;
        }
        
        /* 增加模态框宽度的自定义类 */
        .modal-content.modal-lg {
            max-width: 800px !important;
            width: 90% !important;
        }
        
        /* 内容管理模态框的表格样式 */
        #questionTable td {
            padding: 10px 12px;
        }
        
        /* 增强内容管理模态框样式 */
        #semanticQuestionModal .classifier-info {
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        #semanticQuestionModal .classifier-info h4 {
            font-size: 18px;
            margin-bottom: 5px;
            color: #333;
        }
        
        #semanticQuestionModal .classifier-info p {
            color: #666;
            margin-bottom: 0;
        }
        
        #semanticQuestionModal .question-actions {
            margin-bottom: 15px;
        }
        
        #semanticQuestionModal .table th {
            background-color: #f5f5f5;
            font-weight: 600;
        }
        
        /* 让内容单元格支持多行显示 */
        #questionTable td:nth-child(2) {
            white-space: pre-wrap;
            word-break: break-word;
            max-height: 80px;
            overflow-y: auto !important;
        }
    </style>
</head>
<body>
    <div class="app-wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>AI招聘客服管理系统</h2>
            </div>
            <div class="sidebar-menu">
                <div class="sidebar-menu-item" data-link="index">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表盘</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-briefcase"></i>
                    <span>职位管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="job_classification">
                        <i class="fas fa-tags"></i>
                        <span>职位分类</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="batch_management">
                        <i class="fas fa-layer-group"></i>
                        <span>批量管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="positions">
                        <i class="fas fa-list"></i>
                        <span>职位列表</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="candidates">
                    <i class="fas fa-user-tie"></i>
                    <span>候选人管理</span>
                </div>
                <div class="sidebar-menu-item" data-link="virtual_hr">
                    <i class="fas fa-robot"></i>
                    <span>虚拟HR</span>
                </div>
                <div class="sidebar-menu-item" data-link="scenes">
                    <i class="fas fa-tasks"></i>
                    <span>面试场景</span>
                </div>
                <div class="sidebar-menu-item" data-link="interview">
                    <i class="fas fa-comments"></i>
                    <span>面试互动（仅供测试）</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-star"></i>
                    <span>胜任力模型</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="triggers">
                        <i class="fas fa-bolt"></i>
                        <span>触发器</span>
                    </div>
                    <div class="sidebar-menu-item active" data-link="classifier">
                        <i class="fas fa-filter"></i>
                        <span>分类器</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="ai_brain">
                    <i class="fas fa-brain"></i>
                    <span>AI大脑</span>
                </div>
                <div class="sidebar-menu-item has-submenu open">
                    <i class="fas fa-chart-line"></i>
                    <span>AI数据透视</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu show">
                    <div class="sidebar-menu-item active" data-link="scheduled_analysis">
                        <i class="fas fa-clock"></i>
                        <span>客情分析</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="qa_engine">
                        <i class="fas fa-question-circle"></i>
                        <span>QA引擎</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-key"></i>
                    <span>APIKey管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="apikey_create">
                        <i class="fas fa-plus"></i>
                        <span>创建APIKey</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="apikey_usage">
                        <i class="fas fa-chart-bar"></i>
                        <span>用量统计</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-cog"></i>
                    <span>用户设置</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="channel_management">
                        <i class="fas fa-plug"></i>
                        <span>渠道接入管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="global_settings">
                        <i class="fas fa-sliders-h"></i>
                        <span>全局配置</span>
                    </div>
                </div>
                <div class="sidebar-menu-item admin-only" data-link="user_management07082">
                    <i class="fas fa-user-cog"></i>
                    <span>用户管理</span>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部导航栏 -->
            <div class="topbar">
                <div class="topbar-toggle">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="topbar-title">
                    分类器管理
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <div class="page-header">
                    <h2>分类器管理</h2>
                </div>
                
                <div class="classifier-panels">
                    <div class="panel-column">
                    <!-- 语义分类器面板 -->
                    <div class="card">
                        <div class="card-header">
                            <h3>语义分类器</h3>
                            <button class="btn btn-primary" id="addSemanticBtn">
                                    <i class="fas fa-plus"></i> 新增语义分类器
                            </button>
                        </div>
                        <div class="card-body">
                                <div class="search-container">
                                    <input type="text" class="search-input" id="semanticSearchInput" placeholder="搜索语义分类器...">
                                </div>
                                <div class="table-container">
                                <table class="table" id="semanticClassifierTable">
                                    <thead>
                                        <tr>
                                            <th width="10%">序号</th>
                                                <th width="25%">分类器名称</th>
                                                <th width="15%">内容数量</th>
                                                <th width="20%">创建时间</th>
                                            <th width="30%">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- 语义分类器列表将通过JS动态生成 -->
                                    </tbody>
                                </table>
                            </div>
                                <div class="pagination-container" id="semanticPagination">
                                    <!-- 分页控件将通过JS动态生成 -->
                                </div>
                            </div>
                        </div>

                        <!-- 语义分类器提示卡片 -->
                        <div class="card prompt-card" id="semanticPromptCard">
                            <div class="card-header">
                                <h3>语义分类器提示词</h3>
                            </div>
                            <div class="card-body">
                                <span>可用变量及含义：{name}语义分类器名字,{description}说明,{semantic_classifier_questions}语义内容</span><br>
                            </div>
                            <div class="card-body">
                                <textarea id="semantic_prompt" class="prompt-textarea" placeholder="语义分类器提示词..."></textarea>
                            </div>
                            <div class="card-footer">
                                <button class="btn btn-primary" id="saveSemanticPromptBtn">保存提示</button>
                            </div>
                        </div>
                    </div>

                    <div class="panel-column">
                    <!-- 答案分类器面板 -->
                    <div class="card">
                        <div class="card-header">
                            <h3>答案分类器</h3>
                                <button class="btn btn-primary" id="addAnswerBtn">
                                    <i class="fas fa-plus"></i> 新增答案分类器
                                </button>
                        </div>
                        <div class="card-body">
                                <div class="search-container">
                                    <input type="text" class="search-input" id="answerSearchInput" placeholder="搜索答案分类器...">
                                </div>
                                <div class="table-container">
                                <table class="table" id="answerClassifierTable">
                                    <thead>
                                        <tr>
                                                <th width="10%">序号</th>
                                                <th width="20%">分类器名称</th>
                                                <th width="15%">积极答案倍数</th>
                                                <th width="15%">中性答案倍数</th>
                                                <th width="15%">消极答案倍数</th>
                                                <th width="25%">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- 答案分类器列表将通过JS动态生成 -->
                                    </tbody>
                                </table>
                            </div>
                                <div class="pagination-container" id="answerPagination">
                                    <!-- 分页控件将通过JS动态生成 -->
                                </div>
                            </div>
                        </div>

                        <!-- 答案分类器提示卡片 -->
                        <div class="card prompt-card" id="answerPromptCard">
                            <div class="card-header">
                                <h3>答案分类器提示词</h3>
                            </div>
                            <div class="card-body">
                                <span>可用变量及含义：{answer_classifier_name}答案分类器名字,{description}说明,{active_multiplier}积极答案倍数,{neutral_multiplier}中性答案倍数,{negative_multiplier}消极答案倍数</span>
                            </div>
                            <div class="card-body">
                                <textarea id="answer_prompt" class="prompt-textarea" placeholder="答案分类器提示词..."></textarea>
                            </div>
                            <div class="card-footer">
                                <button class="btn btn-primary" id="saveAnswerPromptBtn">保存提示</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 语义分类器模态框 -->
    <div class="modal" id="semanticModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="semanticModalTitle">新增语义分类器</h3>
                <span class="modal-close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="semanticForm" data-validate="true">
                    <input type="hidden" id="semanticId" name="id">
                    
                    <div class="form-group">
                        <label for="semantic_classifier_name" class="form-label">分类器名称:</label>
                        <input type="text" id="semantic_classifier_name" name="name" class="form-control" required>
                        <small>给分类器起个易于识别的名称</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="semantic_classifier_description" class="form-label">分类器说明:</label>
                        <textarea id="semantic_classifier_description" name="description" class="form-control" rows="3"></textarea>
                        <small>对此分类器的详细说明（可选）</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelSemanticBtn">取消</button>
                <button class="btn btn-primary" id="saveSemanticBtn">保存</button>
            </div>
        </div>
    </div>

    <!-- 语义分类器内容模态框 -->
    <div class="modal" id="semanticQuestionModal">
        <div class="modal-content modal-lg" style="max-width: 800px; width: 90%;">
            <div class="modal-header">
                <h3 id="semanticQuestionModalTitle">分类器内容管理</h3>
                <span class="modal-close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="classifier-info">
                    <h4 id="currentClassifierName"></h4>
                    <p id="currentClassifierDesc"></p>
                </div>
                
                <div class="question-actions">
                    <button class="btn btn-primary" id="addQuestionBtn">
                        <i class="fas fa-plus"></i> 添加内容
                    </button>
                </div>
                
                <div class="table-container" style="overflow: visible; min-height: auto;">
                    <table class="table" id="questionTable">
                        <thead>
                            <tr>
                                <th width="8%">序号</th>
                                <th width="62%">内容</th>
                                <th width="10%">分数</th>
                                <th width="20%">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 内容列表将通过JS动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="closeQuestionsBtn">关闭</button>
            </div>
        </div>
    </div>

    <!-- 语义分类器内容编辑模态框 -->
    <div class="modal" id="questionEditModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="questionEditModalTitle">编辑内容</h3>
                <span class="modal-close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="questionForm" data-validate="true">
                    <input type="hidden" id="questionId" name="id">
                    <input type="hidden" id="questionClassifierId" name="classifier_id">
                    
                    <div class="form-group">
                        <label for="question_content" class="form-label">内容:</label>
                        <textarea id="question_content" name="content" class="form-control" rows="4" required></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="question_score" class="form-label">分数:</label>
                        <input type="number" id="question_score" name="score" class="form-control" step="0.1" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelQuestionBtn">取消</button>
                <button class="btn btn-primary" id="saveQuestionBtn">保存</button>
            </div>
        </div>
    </div>

    <!-- 答案分类器模态框 -->
    <div class="modal" id="answerModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="answerModalTitle">编辑答案分类器</h3>
                <span class="modal-close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="answerForm" data-validate="true">
                    <input type="hidden" id="answerId" name="id">
                    
                    <div class="form-group">
                        <label for="answer_name" class="form-label">分类器名称:</label>
                        <input type="text" id="answer_name" name="name" class="form-control" required>
                        <small>给分类器起个易于识别的名称</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="answer_description" class="form-label">分类器说明:</label>
                        <textarea id="answer_description" name="description" class="form-control" rows="3"></textarea>
                        <small>对此分类器的详细说明（可选）</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="active_multiplier" class="form-label">积极答案倍数:</label>
                        <input type="number" id="active_multiplier" name="active_multiplier" class="form-control" step="0.1" required>
                        <small>积极答案的分数倍数</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="neutral_multiplier" class="form-label">中性答案倍数:</label>
                        <input type="number" id="neutral_multiplier" name="neutral_multiplier" class="form-control" step="0.1" required>
                        <small>中性答案的分数倍数</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="negative_multiplier" class="form-label">消极答案倍数:</label>
                        <input type="number" id="negative_multiplier" name="negative_multiplier" class="form-control" step="0.1" required>
                        <small>消极答案的分数倍数</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelAnswerBtn">取消</button>
                <button class="btn btn-primary" id="saveAnswerBtn">保存</button>
            </div>
        </div>
    </div>

    <!-- 脚本引用 -->
    <script src="/static/js/main.js"></script>
    <script src="/static/js/classifier.js"></script>
    <script src="/static/js/sweetalert2@11"></script>
    <script src="/static/js/auth07082.js"></script>
    <script src="/static/js/auth-check.js"></script>
    <script src="/static/js/user-logout.js"></script>
</body>
</html> 