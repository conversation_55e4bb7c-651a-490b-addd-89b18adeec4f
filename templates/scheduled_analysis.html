<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI招聘客服管理系统 - 定时分析</title>
    <link rel="stylesheet" href="/static/css/all.min.css">
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/scheduled_analysis.css">
</head>
<body>
    <div class="app-wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>AI招聘客服管理系统</h2>
            </div>
            <div class="sidebar-menu">
                <div class="sidebar-menu-item" data-link="index">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表盘</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-briefcase"></i>
                    <span>职位管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="job_classification">
                        <i class="fas fa-tags"></i>
                        <span>职位分类</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="batch_management">
                        <i class="fas fa-layer-group"></i>
                        <span>批量管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="positions">
                        <i class="fas fa-list"></i>
                        <span>职位列表</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="candidates">
                    <i class="fas fa-user-tie"></i>
                    <span>候选人管理</span>
                </div>
                <div class="sidebar-menu-item" data-link="virtual_hr">
                    <i class="fas fa-robot"></i>
                    <span>虚拟HR</span>
                </div>
                <div class="sidebar-menu-item" data-link="scenes">
                    <i class="fas fa-tasks"></i>
                    <span>面试场景</span>
                </div>
                <div class="sidebar-menu-item" data-link="interview">
                    <i class="fas fa-comments"></i>
                    <span>面试互动（仅供测试）</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-star"></i>
                    <span>胜任力模型</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="triggers">
                        <i class="fas fa-bolt"></i>
                        <span>触发器</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="classifier">
                        <i class="fas fa-filter"></i>
                        <span>分类器</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="ai_brain">
                    <i class="fas fa-brain"></i>
                    <span>AI大脑</span>
                </div>
                <div class="sidebar-menu-item has-submenu open">
                    <i class="fas fa-chart-line"></i>
                    <span>AI数据透视</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu show">
                    <div class="sidebar-menu-item active" data-link="scheduled_analysis">
                        <i class="fas fa-clock"></i>
                        <span>客情分析</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="qa_engine">
                        <i class="fas fa-question-circle"></i>
                        <span>QA引擎</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-key"></i>
                    <span>APIKey管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="apikey_create">
                        <i class="fas fa-plus"></i>
                        <span>创建APIKey</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="apikey_usage">
                        <i class="fas fa-chart-bar"></i>
                        <span>用量统计</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-cog"></i>
                    <span>用户设置</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="channel_management">
                        <i class="fas fa-plug"></i>
                        <span>渠道接入管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="global_settings">
                        <i class="fas fa-sliders-h"></i>
                        <span>全局配置</span>
                    </div>
                </div>
                <div class="sidebar-menu-item admin-only" data-link="user_management07082">
                    <i class="fas fa-user-cog"></i>
                    <span>用户管理</span>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部导航栏 -->
            <div class="topbar">
                <div class="topbar-toggle">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="topbar-title">
                    定时分析
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 分析配置面板 -->
                <div class="analysis-config-panel">
                    <div class="panel-header">
                        <h3>分析配置</h3>
                        <button id="createAnalysisBtn" class="btn btn-primary">
                            <i class="fas fa-plus"></i> 新建分析
                        </button>
                    </div>
                    <div class="panel-body">
                        <div class="analysis-list" id="analysisList">
                            <!-- 分析配置列表将通过JS动态加载 -->
                        </div>
                    </div>
                </div>

                <!-- 用户列表面板 -->
                <div class="users-panel">
                    <div class="panel-header">
                        <h3>待分析用户列表</h3>
                        <div class="panel-actions">
                            <button id="refreshUsersBtn" class="btn btn-secondary">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </div>
                    </div>
                    <div class="panel-body">
                        <div class="users-list" id="usersList">
                            <!-- 用户列表将通过JS动态加载 -->
                        </div>
                    </div>
                </div>

                <!-- 执行状态面板 -->
                <div class="execution-status-panel">
                    <div class="panel-header">
                        <h3>执行状态</h3>
                    </div>
                    <div class="panel-body">
                        <div class="status-list" id="statusList">
                            <!-- 执行状态将通过JS动态加载 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新建/编辑分析配置模态框 -->
    <div class="modal fade" id="analysisConfigModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">新建分析配置</h5>
                    <button type="button" class="btn-close" id="modalCloseBtn" aria-label="关闭">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="analysisConfigForm">
                        <input type="hidden" id="analysisId" name="analysisId">
                        
                        <div class="form-group mb-3">
                            <label for="analysisName" class="form-label">分析名称</label>
                            <input type="text" class="form-control" id="analysisName" name="name" required>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="jobClassificationId" class="form-label">职位分类</label>
                            <select class="form-control" id="jobClassificationId" name="job_classification_id">
                                <option value="">全部职位（不限制职位分类）</option>
                                <!-- 职位分类选项将通过JS动态加载 -->
                            </select>
                            <div class="form-text">选择要分析的职位分类，不选择则分析所有职位的用户</div>
                        </div>

                        <div class="form-group mb-3">
                            <label for="analysisPrompt" class="form-label">分析提示词</label>
                            <textarea class="form-control" id="analysisPrompt" name="prompt" rows="6" required
                                placeholder="请输入用于分析用户聊天记录的提示词..."></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="scheduleTime" class="form-label">执行时间</label>
                                    <input type="time" class="form-control" id="scheduleTime" name="schedule_time" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="timeRangeDays" class="form-label">分析时间范围（天）</label>
                                    <input type="number" class="form-control" id="timeRangeDays" name="time_range_days" 
                                           min="1" max="30" value="1" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="isAutoEnabled" name="is_auto_enabled" checked>
                                <label class="form-check-label" for="isAutoEnabled">
                                    启用自动执行
                                </label>
                            </div>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="emailRecipients" class="form-label">邮件接收者</label>
                            <input type="email" class="form-control" id="emailRecipients" name="email_recipients" 
                                   placeholder="多个邮箱用逗号分隔，如：<EMAIL>,<EMAIL>">
                            <div class="form-text">分析完成后将发送报告到指定邮箱</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="modalCancelBtn">取消</button>
                    <button type="button" class="btn btn-primary" id="saveAnalysisBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 执行结果查看模态框 -->
    <div class="modal fade" id="executionResultModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">执行结果详情</h5>
                    <button type="button" class="btn-close" id="resultModalCloseBtn" aria-label="关闭">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="execution-result-content">
                        <div class="result-header">
                            <h6>执行信息</h6>
                            <div class="execution-info" id="executionInfo">
                                <!-- 执行信息将通过JS动态加载 -->
                            </div>
                        </div>
                        <div class="result-content">
                            <h6>执行结果</h6>
                            <div class="markdown-content" id="markdownContent">
                                <!-- Markdown内容将通过JS动态加载和渲染 -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="resultModalCancelBtn">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 脚本引用 -->
    <script src="/static/js/main.js"></script>
    <script src="/static/js/api.js"></script>
    <script src="/static/js/sweetalert2@11"></script>
    <!-- 添加markdown渲染库 -->
    <script src="/static/js/marked.min.js"></script>
    <script src="/static/js/scheduled_analysis.js"></script>
    <script src="/static/js/auth07082.js"></script>
    <script src="/static/js/auth-check.js"></script>
    <script src="/static/js/user-logout.js"></script>
</body>
</html>
