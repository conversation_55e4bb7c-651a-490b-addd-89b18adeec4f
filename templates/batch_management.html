<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI招聘客服管理系统 - 批量管理</title>
    <link rel="stylesheet" href="/static/css/all.min.css">
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/batch_management.css">
</head>
<body>
    <div class="app-wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>AI招聘客服管理系统</h2>
            </div>
            <div class="sidebar-menu">
                <div class="sidebar-menu-item" data-link="index">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表盘</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-briefcase"></i>
                    <span>职位管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="job_classification">
                        <i class="fas fa-tags"></i>
                        <span>职位分类</span>
                    </div>
                    <div class="sidebar-menu-item active" data-link="batch_management">
                        <i class="fas fa-layer-group"></i>
                        <span>批量管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="positions">
                        <i class="fas fa-list"></i>
                        <span>职位列表</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="candidates">
                    <i class="fas fa-user-tie"></i>
                    <span>候选人管理</span>
                </div>
                <div class="sidebar-menu-item" data-link="virtual_hr">
                    <i class="fas fa-robot"></i>
                    <span>虚拟HR</span>
                </div>
                <div class="sidebar-menu-item" data-link="scenes">
                    <i class="fas fa-tasks"></i>
                    <span>面试场景</span>
                </div>
                <div class="sidebar-menu-item" data-link="interview">
                    <i class="fas fa-comments"></i>
                    <span>面试互动（仅供测试）</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-star"></i>
                    <span>胜任力模型</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="triggers">
                        <i class="fas fa-cogs"></i>
                        <span>触发器</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="classifier">
                        <i class="fas fa-filter"></i>
                        <span>分类器</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="ai_brain">
                    <i class="fas fa-brain"></i>
                    <span>AI大脑</span>
                </div>
                <div class="sidebar-menu-item has-submenu open">
                    <i class="fas fa-chart-line"></i>
                    <span>AI数据透视</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu show">
                    <div class="sidebar-menu-item active" data-link="scheduled_analysis">
                        <i class="fas fa-clock"></i>
                        <span>客情分析</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="qa_engine">
                        <i class="fas fa-question-circle"></i>
                        <span>QA引擎</span>
                    </div>
                </div>
                    <div class="sidebar-menu-item has-submenu">
                        <i class="fas fa-key"></i>
                        <span>APIKey管理</span>
                        <i class="fas fa-chevron-down submenu-arrow"></i>
                    </div>
                    <div class="sidebar-submenu">
                        <div class="sidebar-menu-item" data-link="apikey_create">
                            <i class="fas fa-plus"></i>
                            <span>创建APIKey</span>
                        </div>
                        <div class="sidebar-menu-item" data-link="apikey_usage">
                            <i class="fas fa-chart-bar"></i>
                            <span>用量统计</span>
                        </div>
                    </div>
                    <div class="sidebar-menu-item has-submenu">
                        <i class="fas fa-cog"></i>
                        <span>用户设置</span>
                        <i class="fas fa-chevron-down submenu-arrow"></i>
                    </div>
                    <div class="sidebar-submenu">
                        <div class="sidebar-menu-item" data-link="channel_management">
                            <i class="fas fa-plug"></i>
                            <span>渠道接入管理</span>
                        </div>
                        <div class="sidebar-menu-item" data-link="global_settings">
                            <i class="fas fa-sliders-h"></i>
                            <span>全局配置</span>
                        </div>
                    </div>
                    <div class="sidebar-menu-item admin-only" data-link="user_management07082">
                        <i class="fas fa-user-cog"></i>
                        <span>用户管理</span>
                    </div>
                </div>
            </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 顶部导航栏 -->
            <div class="topbar">
                <div class="topbar-toggle">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="topbar-title">
                    批量管理
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 页面标题和操作 -->
                <div class="page-header">
                    <h2>批量管理列表</h2>
                    <div class="header-actions">
                        <button class="btn btn-success" id="importBtn">
                            <i class="fas fa-upload"></i> 导入数据
                        </button>
                        <button class="btn btn-info" id="exportBtn">
                            <i class="fas fa-download"></i> 导出数据
                        </button>
                        <button class="btn btn-primary" id="addRecordBtn">
                            <i class="fas fa-plus"></i> 新增记录
                        </button>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="card mb-20">
                    <div class="card-body">
                        <div class="filter-container">
                            <div class="search-box">
                                <input type="text" id="searchInput" class="form-control" placeholder="搜索城市、岗位分类...">
                                <button class="btn btn-primary" id="searchBtn">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table" id="batchTable">
                                <thead>
                                    <tr>
                                        <th>
                                            <input type="checkbox" id="selectAll">
                                        </th>
                                        <th>城市</th>
                                        <th>岗位分类</th>
                                        <th>工作内容</th>
                                        <th>招聘要求</th>
                                        <th>工作时间</th>
                                        <th>薪酬福利</th>
                                        <th>面试时间</th>
                                        <th>培训时间</th>
                                        <th>招聘区域</th>
                                        <th>面试地址</th>
                                        <th>备注</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="batchTableBody">
                                    <!-- 数据行将通过JavaScript动态生成 -->
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <div class="pagination-container" id="pagination">
                            <!-- 分页将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增/编辑记录模态框 -->
    <div class="modal" id="recordModal">
        <div class="modal-content record-form-modal">
            <div class="modal-header">
                <h3 id="modalTitle">新增记录</h3>
                <span class="modal-close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="recordForm" data-validate="true">
                    <input type="hidden" id="recordId" name="id">
                    
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label for="city" class="form-label">城市 <span class="required">*</span>:</label>
                                <input type="text" id="city" name="city" class="form-control" placeholder="如：北京、上海、深圳" required>
                            </div>

                            <div class="form-group">
                                <label for="job_category" class="form-label">岗位分类 <span class="required">*</span>:</label>
                                <input type="text" id="job_category" name="job_category" class="form-control" placeholder="如：软件开发、产品经理、UI设计" required>
                            </div>

                            <div class="form-group">
                                <label for="work_content" class="form-label">工作内容:</label>
                                <textarea id="work_content" name="work_content" class="form-control" rows="3" placeholder="详细描述岗位的主要工作职责和内容..."></textarea>
                            </div>

                            <div class="form-group">
                                <label for="recruitment_requirements" class="form-label">招聘要求:</label>
                                <textarea id="recruitment_requirements" name="recruitment_requirements" class="form-control" rows="3" placeholder="学历要求、工作经验、技能要求等..."></textarea>
                            </div>

                            <div class="form-group">
                                <label for="work_time" class="form-label">工作时间:</label>
                                <input type="text" id="work_time" name="work_time" class="form-control" placeholder="如：9:00-18:00，双休">
                            </div>

                            <div class="form-group">
                                <label for="salary_benefits" class="form-label">薪酬福利:</label>
                                <textarea id="salary_benefits" name="salary_benefits" class="form-control" rows="2" placeholder="薪资范围、五险一金、其他福利..."></textarea>
                            </div>
                        </div>
                        
                        <div class="form-col">
                            <div class="form-group">
                                <label for="interview_time" class="form-label">面试时间:</label>
                                <input type="text" id="interview_time" name="interview_time" class="form-control" placeholder="如：周一至周五 14:00-17:00">
                            </div>

                            <div class="form-group">
                                <label for="training_time" class="form-label">培训时间:</label>
                                <input type="text" id="training_time" name="training_time" class="form-control" placeholder="如：入职后1个月">
                            </div>

                            <div class="form-group">
                                <label for="recruitment_area" class="form-label">招聘区域:</label>
                                <input type="text" id="recruitment_area" name="recruitment_area" class="form-control" placeholder="如：海淀区、朝阳区">
                            </div>

                            <div class="form-group">
                                <label for="interview_address" class="form-label">面试地址:</label>
                                <textarea id="interview_address" name="interview_address" class="form-control" rows="2" placeholder="详细的面试地址，包括楼层、房间号等..."></textarea>
                            </div>

                            <div class="form-group">
                                <label for="remarks" class="form-label">备注:</label>
                                <textarea id="remarks" name="remarks" class="form-control" rows="3" placeholder="其他需要说明的信息..."></textarea>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-light" id="cancelBtn">取消</button>
                <button class="btn btn-primary" id="saveRecordBtn">保存</button>
            </div>
        </div>
    </div>

    <!-- 导入数据模态框 -->
    <div class="modal" id="importModal">
        <div class="modal-content import-modal">
            <div class="modal-header">
                <h3>导入数据</h3>
                <span class="modal-close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="import-section">
                    <h4>选择文件</h4>
                    <div class="file-upload-area" id="fileUploadArea">
                        <input type="file" id="fileInput" accept=".xlsx,.xls,.csv" style="display: none;">
                        <div class="upload-placeholder">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <p>点击选择文件或拖拽文件到此处</p>
                            <p class="file-types">支持 Excel (.xlsx, .xls) 和 CSV 文件</p>
                        </div>
                    </div>
                    <div class="selected-file" id="selectedFile" style="display: none;">
                        <i class="fas fa-file"></i>
                        <span class="file-name"></span>
                        <button class="btn-remove-file" id="removeFileBtn">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                
                <div class="import-options">
                    <h4>导入选项</h4>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="skipFirstRow" checked>
                            跳过第一行（标题行）
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="updateExisting">
                            更新已存在的记录
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-light" id="cancelImportBtn">取消</button>
                <button class="btn btn-primary" id="startImportBtn" disabled>开始导入</button>
            </div>
        </div>
    </div>

    <!-- 脚本引用 -->
    <script src="/static/js/main.js"></script>
    <script src="/static/js/api.js"></script>
    <script src="/static/js/batch_management.js"></script>
    <script src="/static/js/sweetalert2@11"></script>
    <script src="/static/js/auth07082.js"></script>
    <script src="/static/js/auth-check.js"></script>
    <script src="/static/js/user-logout.js"></script>
</body>
</html>
