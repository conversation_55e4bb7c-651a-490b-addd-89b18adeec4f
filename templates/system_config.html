<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI招聘客服管理系统 - 系统配置</title>
    <link rel="stylesheet" href="/static/css/all.min.css">
    <link rel="stylesheet" href="/static/css/main.css">
    <style>
        /* 系统配置页面特定样式 */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            padding: 20px 0;
        }

        .config-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.3s;
        }

        .config-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .config-card-header {
            display: flex;
            align-items: center;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-bottom: 1px solid var(--border-color);
        }

        .config-card-icon {
            width: 50px;
            height: 50px;
            background: var(--primary-color);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            margin-right: 15px;
        }

        .config-card-title h3 {
            margin: 0 0 5px 0;
            font-size: 18px;
            font-weight: 600;
            color: var(--text-dark);
        }

        .config-card-title p {
            margin: 0;
            font-size: 14px;
            color: var(--text-muted);
        }

        .config-card-body {
            padding: 20px;
        }

        .upload-area-compact {
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s;
            cursor: pointer;
            margin-bottom: 15px;
        }

        .upload-area-compact:hover {
            border-color: var(--primary-color);
            background: rgba(52, 152, 219, 0.05);
        }

        .upload-area-compact.dragover {
            border-color: var(--primary-color);
            background: rgba(52, 152, 219, 0.1);
        }

        .upload-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            color: var(--text-muted);
        }

        .upload-content i {
            font-size: 20px;
        }

        .qr-list {
            max-height: 200px;
            overflow-y: auto;
        }

        .qr-item-compact {
            display: flex;
            align-items: center;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            margin-bottom: 8px;
            background: white;
            transition: all 0.3s;
        }

        .qr-item-compact:hover {
            background: #f8f9fa;
        }

        .qr-thumbnail {
            width: 40px;
            height: 40px;
            object-fit: cover;
            border-radius: 4px;
            margin-right: 10px;
        }

        .qr-item-info {
            flex: 1;
        }

        .qr-item-name {
            font-weight: 500;
            font-size: 14px;
            margin-bottom: 2px;
        }

        .qr-item-meta {
            font-size: 12px;
            color: var(--text-muted);
        }

        .qr-item-actions {
            display: flex;
            gap: 5px;
        }

        .btn-delete-small {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-delete-small:hover {
            background: #c0392b;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 500;
            color: var(--text-dark);
        }

        .info-value {
            color: var(--text-muted);
        }

        .status-running {
            color: #28a745 !important;
        }

        .status-running i {
            margin-right: 5px;
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .setting-item:last-child {
            border-bottom: none;
        }

        .setting-info {
            flex: 1;
        }

        .setting-label {
            display: block;
            font-weight: 500;
            color: var(--text-dark);
            margin-bottom: 2px;
        }

        .setting-desc {
            font-size: 12px;
            color: var(--text-muted);
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--primary-color);
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: var(--border-color);
            border-radius: 2px;
            overflow: hidden;
            margin-top: 10px;
            display: none;
        }

        .progress-fill {
            height: 100%;
            background: var(--primary-color);
            width: 0%;
            transition: width 0.3s;
        }

        .upload-status {
            margin-top: 10px;
            padding: 8px 12px;
            border-radius: 4px;
            display: none;
            font-size: 14px;
        }

        .upload-status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .upload-status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .btn-block {
            width: 100%;
        }

        .mb-10 {
            margin-bottom: 10px;
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
            background: transparent;
        }

        .btn-outline-primary:hover {
            background: var(--primary-color);
            color: white;
        }

        .btn-outline-warning {
            color: #ffc107;
            border: 1px solid #ffc107;
            background: transparent;
        }

        .btn-outline-warning:hover {
            background: #ffc107;
            color: white;
        }

        .btn-outline-info {
            color: #17a2b8;
            border: 1px solid #17a2b8;
            background: transparent;
        }

        .btn-outline-info:hover {
            background: #17a2b8;
            color: white;
        }
    </style>
</head>
<body>
    <div class="app-wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>AI招聘客服管理系统</h2>
            </div>
            <div class="sidebar-menu">
                <div class="sidebar-menu-item" data-link="index">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表盘</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-briefcase"></i>
                    <span>职位管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="job_classification">
                        <i class="fas fa-tags"></i>
                        <span>职位分类</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="batch_management">
                        <i class="fas fa-layer-group"></i>
                        <span>批量管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="positions">
                        <i class="fas fa-list"></i>
                        <span>职位列表</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="candidates">
                    <i class="fas fa-user-tie"></i>
                    <span>候选人管理</span>
                </div>
                <div class="sidebar-menu-item" data-link="virtual_hr">
                    <i class="fas fa-robot"></i>
                    <span>虚拟HR</span>
                </div>
                <div class="sidebar-menu-item" data-link="scenes">
                    <i class="fas fa-tasks"></i>
                    <span>面试场景</span>
                </div>
                <div class="sidebar-menu-item" data-link="interview">
                    <i class="fas fa-comments"></i>
                    <span>面试互动（仅供测试）</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-star"></i>
                    <span>胜任力模型</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="triggers">
                        <i class="fas fa-bolt"></i>
                        <span>触发器</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="classifier">
                        <i class="fas fa-filter"></i>
                        <span>分类器</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="ai_brain">
                    <i class="fas fa-brain"></i>
                    <span>AI大脑</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-chart-line"></i>
                    <span>AI数据透视</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="scheduled_analysis">
                        <i class="fas fa-clock"></i>
                        <span>客情分析</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="qa_engine">
                        <i class="fas fa-question-circle"></i>
                        <span>QA引擎</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-key"></i>
                    <span>APIKey管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="apikey_create">
                        <i class="fas fa-plus"></i>
                        <span>创建APIKey</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="apikey_usage">
                        <i class="fas fa-chart-bar"></i>
                        <span>用量统计</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-cog"></i>
                    <span>用户设置</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="channel_management">
                        <i class="fas fa-plug"></i>
                        <span>渠道接入管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="global_settings">
                        <i class="fas fa-sliders-h"></i>
                        <span>全局配置</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu open">
                    <i class="fas fa-cogs"></i>
                    <span>系统设置</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu show">
                    <div class="sidebar-menu-item admin-only" data-link="user_management07082">
                        <i class="fas fa-user-cog"></i>
                        <span>用户管理</span>
                    </div>
                    <div class="sidebar-menu-item admin-only active" data-link="system_config">
                        <i class="fas fa-wrench"></i>
                        <span>系统配置</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部导航栏 -->
            <div class="topbar">
                <div class="topbar-toggle">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="topbar-title">
                    系统配置
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 系统配置控制面板 -->
                <div class="dashboard-grid">
                    <!-- 群二维码管理卡片 -->
                    <div class="config-card">
                        <div class="config-card-header">
                            <div class="config-card-icon">
                                <i class="fas fa-qrcode"></i>
                            </div>
                            <div class="config-card-title">
                                <h3>群二维码管理</h3>
                                <p>管理系统中的群二维码图片</p>
                            </div>
                        </div>
                        <div class="config-card-body">
                            <!-- 上传区域 -->
                            <div class="upload-area-compact" id="uploadArea">
                                <div class="upload-content">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <span>点击上传二维码</span>
                                </div>
                                <input type="file" id="fileInput" accept="image/*" style="display: none;">
                            </div>

                            <!-- 进度条 -->
                            <div class="progress-bar" id="progressBar">
                                <div class="progress-fill" id="progressFill"></div>
                            </div>

                            <!-- 上传状态 -->
                            <div class="upload-status" id="uploadStatus"></div>

                            <!-- 二维码列表 -->
                            <div class="qr-list" id="qrGallery">
                                <!-- 动态加载二维码列表 -->
                            </div>
                        </div>
                    </div>

                    <!-- 系统信息卡片 -->
                    <div class="config-card">
                        <div class="config-card-header">
                            <div class="config-card-icon">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <div class="config-card-title">
                                <h3>系统信息</h3>
                                <p>查看系统运行状态和基本信息</p>
                            </div>
                        </div>
                        <div class="config-card-body">
                            <div class="info-item">
                                <span class="info-label">系统版本:</span>
                                <span class="info-value">v1.0.0</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">运行状态:</span>
                                <span class="info-value status-running">
                                    <i class="fas fa-circle"></i> 正常运行
                                </span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">数据库状态:</span>
                                <span class="info-value status-running">
                                    <i class="fas fa-circle"></i> 连接正常
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- 系统维护卡片 -->
                    <div class="config-card">
                        <div class="config-card-header">
                            <div class="config-card-icon">
                                <i class="fas fa-tools"></i>
                            </div>
                            <div class="config-card-title">
                                <h3>系统维护</h3>
                                <p>系统维护和管理工具</p>
                            </div>
                        </div>
                        <div class="config-card-body">
                            <button class="btn btn-outline-primary btn-block mb-10">
                                <i class="fas fa-database"></i> 数据库备份
                            </button>
                            <button class="btn btn-outline-warning btn-block mb-10">
                                <i class="fas fa-broom"></i> 清理缓存
                            </button>
                            <button class="btn btn-outline-info btn-block">
                                <i class="fas fa-download"></i> 导出日志
                            </button>
                        </div>
                    </div>

                    <!-- 安全设置卡片 -->
                    <div class="config-card">
                        <div class="config-card-header">
                            <div class="config-card-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="config-card-title">
                                <h3>安全设置</h3>
                                <p>系统安全相关配置</p>
                            </div>
                        </div>
                        <div class="config-card-body">
                            <div class="setting-item">
                                <div class="setting-info">
                                    <span class="setting-label">登录超时时间</span>
                                    <span class="setting-desc">用户登录会话超时时间</span>
                                </div>
                                <select class="form-control form-control-sm">
                                    <option value="30">30分钟</option>
                                    <option value="60" selected>1小时</option>
                                    <option value="120">2小时</option>
                                    <option value="480">8小时</option>
                                </select>
                            </div>
                            <div class="setting-item">
                                <div class="setting-info">
                                    <span class="setting-label">密码复杂度</span>
                                    <span class="setting-desc">要求密码包含数字和字母</span>
                                </div>
                                <label class="switch">
                                    <input type="checkbox" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 脚本引用 -->
    <script src="/static/js/main.js"></script>
    <script src="/static/js/api.js"></script>
    <script src="/static/js/sweetalert2@11"></script>
    <script src="/static/js/auth07082.js"></script>
    <script src="/static/js/auth-check.js"></script>
    <script src="/static/js/user-logout.js"></script>
    <script>
        // 系统配置页面脚本
        document.addEventListener('DOMContentLoaded', function() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressFill');
            const uploadStatus = document.getElementById('uploadStatus');
            const qrGallery = document.getElementById('qrGallery');

            // 加载二维码列表
            loadQRCodes();

            // 点击上传区域
            uploadArea.addEventListener('click', () => {
                fileInput.click();
            });

            // 文件选择
            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    uploadFile(e.target.files[0]);
                }
            });

            // 拖拽上传
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    uploadFile(files[0]);
                }
            });

            // 上传文件
            function uploadFile(file) {
                // 检查文件类型
                if (!file.type.startsWith('image/')) {
                    showUploadStatus('请选择图片文件', 'error');
                    return;
                }

                // 检查文件大小 (5MB)
                if (file.size > 5 * 1024 * 1024) {
                    showUploadStatus('文件大小不能超过5MB', 'error');
                    return;
                }

                const formData = new FormData();
                formData.append('file', file);

                // 显示进度条
                progressBar.style.display = 'block';
                uploadStatus.style.display = 'none';

                fetch('/api/system/upload-qr-code', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
                    },
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    progressBar.style.display = 'none';

                    if (data.code === 200) {
                        showUploadStatus('上传成功！', 'success');
                        loadQRCodes(); // 重新加载列表
                        fileInput.value = ''; // 清空文件选择
                    } else {
                        showUploadStatus(data.message || '上传失败', 'error');
                    }
                })
                .catch(error => {
                    progressBar.style.display = 'none';
                    showUploadStatus('上传失败：' + error.message, 'error');
                });
            }

            // 显示上传状态
            function showUploadStatus(message, type) {
                uploadStatus.textContent = message;
                uploadStatus.className = `upload-status ${type}`;
                uploadStatus.style.display = 'block';

                setTimeout(() => {
                    uploadStatus.style.display = 'none';
                }, 3000);
            }

            // 加载二维码列表
            function loadQRCodes() {
                fetch('/api/system/qr-codes', {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        renderQRCodes(data.data);
                    } else {
                        console.error('加载二维码列表失败:', data.message);
                    }
                })
                .catch(error => {
                    console.error('加载二维码列表失败:', error);
                });
            }

            // 渲染二维码列表
            function renderQRCodes(qrCodes) {
                if (qrCodes.length === 0) {
                    qrGallery.innerHTML = '<p style="text-align: center; color: #999; padding: 20px;">暂无上传的二维码</p>';
                    return;
                }

                qrGallery.innerHTML = qrCodes.map(qr => `
                    <div class="qr-item-compact">
                        <img src="/${qr.file_path}" alt="${qr.filename}" class="qr-thumbnail" onerror="this.src='/static/img/placeholder.png'">
                        <div class="qr-item-info">
                            <div class="qr-item-name">${qr.filename}</div>
                            <div class="qr-item-meta">
                                ${formatFileSize(qr.file_size)} • ${formatDateTime(qr.upload_time)}
                            </div>
                        </div>
                        <div class="qr-item-actions">
                            <button class="btn-delete-small" onclick="deleteQRCode('${qr.filename}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `).join('');
            }

            // 删除二维码
            window.deleteQRCode = function(filename) {
                if (!confirm('确定要删除这个二维码吗？')) {
                    return;
                }

                fetch(`/api/system/qr-codes/${filename}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        showUploadStatus('删除成功', 'success');
                        loadQRCodes();
                    } else {
                        showUploadStatus(data.message || '删除失败', 'error');
                    }
                })
                .catch(error => {
                    showUploadStatus('删除失败：' + error.message, 'error');
                });
            };

            // 格式化文件大小
            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // 格式化日期时间
            function formatDateTime(dateString) {
                const date = new Date(dateString);
                return date.toLocaleString('zh-CN');
            }
        });
    </script>
</body>
</html>
