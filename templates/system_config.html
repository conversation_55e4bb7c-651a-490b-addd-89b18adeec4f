<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI招聘客服管理系统 - 系统配置</title>
    <link rel="stylesheet" href="/static/css/all.min.css">
    <link rel="stylesheet" href="/static/css/main.css">
    <style>
        /* 系统配置页面特定样式 */
        .config-section {
            background: white;
            border-radius: 8px;
            box-shadow: var(--shadow);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .config-header {
            background: linear-gradient(135deg, var(--primary-color), #2980b9);
            color: white;
            padding: 20px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .config-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }
        
        .config-body {
            padding: 20px;
        }
        
        .upload-area {
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: var(--primary-color);
            background: rgba(52, 152, 219, 0.05);
        }
        
        .upload-area.dragover {
            border-color: var(--primary-color);
            background: rgba(52, 152, 219, 0.1);
        }
        
        .upload-icon {
            font-size: 48px;
            color: var(--text-muted);
            margin-bottom: 15px;
        }
        
        .upload-text {
            color: var(--text-muted);
            margin-bottom: 15px;
        }
        
        .upload-hint {
            font-size: 12px;
            color: var(--text-muted);
        }
        
        .qr-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .qr-item {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
            background: white;
            transition: all 0.3s;
        }
        
        .qr-item:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        
        .qr-image {
            width: 100%;
            height: 150px;
            object-fit: cover;
            background: #f8f9fa;
        }
        
        .qr-info {
            padding: 15px;
        }
        
        .qr-filename {
            font-weight: 600;
            margin-bottom: 5px;
            word-break: break-all;
        }
        
        .qr-meta {
            font-size: 12px;
            color: var(--text-muted);
            margin-bottom: 10px;
        }
        
        .qr-actions {
            display: flex;
            gap: 8px;
        }
        
        .btn-delete {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .btn-delete:hover {
            background: #c0392b;
        }
        
        .progress-bar {
            width: 100%;
            height: 4px;
            background: var(--border-color);
            border-radius: 2px;
            overflow: hidden;
            margin-top: 10px;
            display: none;
        }
        
        .progress-fill {
            height: 100%;
            background: var(--primary-color);
            width: 0%;
            transition: width 0.3s;
        }
        
        .upload-status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        
        .upload-status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .upload-status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="app-wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>AI招聘客服管理系统</h2>
            </div>
            <div class="sidebar-menu">
                <div class="sidebar-menu-item" data-link="index">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表盘</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-briefcase"></i>
                    <span>职位管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="job_classification">
                        <i class="fas fa-tags"></i>
                        <span>职位分类</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="batch_management">
                        <i class="fas fa-layer-group"></i>
                        <span>批量管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="positions">
                        <i class="fas fa-list"></i>
                        <span>职位列表</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="candidates">
                    <i class="fas fa-user-tie"></i>
                    <span>候选人管理</span>
                </div>
                <div class="sidebar-menu-item" data-link="virtual_hr">
                    <i class="fas fa-robot"></i>
                    <span>虚拟HR</span>
                </div>
                <div class="sidebar-menu-item" data-link="scenes">
                    <i class="fas fa-tasks"></i>
                    <span>面试场景</span>
                </div>
                <div class="sidebar-menu-item" data-link="interview">
                    <i class="fas fa-comments"></i>
                    <span>面试互动（仅供测试）</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-star"></i>
                    <span>胜任力模型</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="triggers">
                        <i class="fas fa-bolt"></i>
                        <span>触发器</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="classifier">
                        <i class="fas fa-filter"></i>
                        <span>分类器</span>
                    </div>
                </div>
                <div class="sidebar-menu-item" data-link="ai_brain">
                    <i class="fas fa-brain"></i>
                    <span>AI大脑</span>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-chart-line"></i>
                    <span>AI数据透视</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="scheduled_analysis">
                        <i class="fas fa-clock"></i>
                        <span>客情分析</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="qa_engine">
                        <i class="fas fa-question-circle"></i>
                        <span>QA引擎</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-key"></i>
                    <span>APIKey管理</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="apikey_create">
                        <i class="fas fa-plus"></i>
                        <span>创建APIKey</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="apikey_usage">
                        <i class="fas fa-chart-bar"></i>
                        <span>用量统计</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu">
                    <i class="fas fa-cog"></i>
                    <span>用户设置</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu">
                    <div class="sidebar-menu-item" data-link="channel_management">
                        <i class="fas fa-plug"></i>
                        <span>渠道接入管理</span>
                    </div>
                    <div class="sidebar-menu-item" data-link="global_settings">
                        <i class="fas fa-sliders-h"></i>
                        <span>全局配置</span>
                    </div>
                </div>
                <div class="sidebar-menu-item has-submenu open">
                    <i class="fas fa-cogs"></i>
                    <span>系统设置</span>
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </div>
                <div class="sidebar-submenu show">
                    <div class="sidebar-menu-item admin-only" data-link="user_management07082">
                        <i class="fas fa-user-cog"></i>
                        <span>用户管理</span>
                    </div>
                    <div class="sidebar-menu-item admin-only active" data-link="system_config">
                        <i class="fas fa-wrench"></i>
                        <span>系统配置</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 顶部导航栏 -->
            <div class="topbar">
                <div class="topbar-toggle">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="topbar-title">
                    系统配置
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 群二维码管理 -->
                <div class="config-section">
                    <div class="config-header">
                        <h3><i class="fas fa-qrcode"></i> 群二维码管理</h3>
                    </div>
                    <div class="config-body">
                        <!-- 上传区域 -->
                        <div class="upload-area" id="uploadArea">
                            <div class="upload-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <div class="upload-text">
                                <strong>点击或拖拽文件到此处上传</strong>
                            </div>
                            <div class="upload-hint">
                                支持 JPG、PNG、GIF、BMP 格式，文件大小不超过 5MB
                            </div>
                            <input type="file" id="fileInput" accept="image/*" style="display: none;">
                        </div>
                        
                        <!-- 进度条 -->
                        <div class="progress-bar" id="progressBar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        
                        <!-- 上传状态 -->
                        <div class="upload-status" id="uploadStatus"></div>
                        
                        <!-- 二维码列表 -->
                        <div class="qr-gallery" id="qrGallery">
                            <!-- 动态加载二维码列表 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入公共脚本 -->
    <script src="/static/js/common.js"></script>
    <script>
        // 系统配置页面脚本
        document.addEventListener('DOMContentLoaded', function() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressFill');
            const uploadStatus = document.getElementById('uploadStatus');
            const qrGallery = document.getElementById('qrGallery');

            // 加载二维码列表
            loadQRCodes();

            // 点击上传区域
            uploadArea.addEventListener('click', () => {
                fileInput.click();
            });

            // 文件选择
            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    uploadFile(e.target.files[0]);
                }
            });

            // 拖拽上传
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    uploadFile(files[0]);
                }
            });

            // 上传文件
            function uploadFile(file) {
                const formData = new FormData();
                formData.append('file', file);

                // 显示进度条
                progressBar.style.display = 'block';
                uploadStatus.style.display = 'none';

                fetch('/api/system/upload-qr-code', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    progressBar.style.display = 'none';
                    
                    if (data.code === 200) {
                        showUploadStatus('上传成功！', 'success');
                        loadQRCodes(); // 重新加载列表
                        fileInput.value = ''; // 清空文件选择
                    } else {
                        showUploadStatus(data.message || '上传失败', 'error');
                    }
                })
                .catch(error => {
                    progressBar.style.display = 'none';
                    showUploadStatus('上传失败：' + error.message, 'error');
                });
            }

            // 显示上传状态
            function showUploadStatus(message, type) {
                uploadStatus.textContent = message;
                uploadStatus.className = `upload-status ${type}`;
                uploadStatus.style.display = 'block';
                
                setTimeout(() => {
                    uploadStatus.style.display = 'none';
                }, 3000);
            }

            // 加载二维码列表
            function loadQRCodes() {
                fetch('/api/system/qr-codes', {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        renderQRCodes(data.data);
                    }
                })
                .catch(error => {
                    console.error('加载二维码列表失败:', error);
                });
            }

            // 渲染二维码列表
            function renderQRCodes(qrCodes) {
                if (qrCodes.length === 0) {
                    qrGallery.innerHTML = '<p style="text-align: center; color: #999; padding: 40px;">暂无上传的二维码</p>';
                    return;
                }

                qrGallery.innerHTML = qrCodes.map(qr => `
                    <div class="qr-item">
                        <img src="/${qr.file_path}" alt="${qr.filename}" class="qr-image" onerror="this.src='/static/img/placeholder.png'">
                        <div class="qr-info">
                            <div class="qr-filename">${qr.filename}</div>
                            <div class="qr-meta">
                                大小: ${formatFileSize(qr.file_size)}<br>
                                上传时间: ${formatDateTime(qr.upload_time)}
                            </div>
                            <div class="qr-actions">
                                <button class="btn-delete" onclick="deleteQRCode('${qr.filename}')">
                                    <i class="fas fa-trash"></i> 删除
                                </button>
                            </div>
                        </div>
                    </div>
                `).join('');
            }

            // 删除二维码
            window.deleteQRCode = function(filename) {
                if (!confirm('确定要删除这个二维码吗？')) {
                    return;
                }

                fetch(`/api/system/qr-codes/${filename}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        showUploadStatus('删除成功', 'success');
                        loadQRCodes();
                    } else {
                        showUploadStatus(data.message || '删除失败', 'error');
                    }
                })
                .catch(error => {
                    showUploadStatus('删除失败：' + error.message, 'error');
                });
            };

            // 格式化文件大小
            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // 格式化日期时间
            function formatDateTime(dateString) {
                const date = new Date(dateString);
                return date.toLocaleString('zh-CN');
            }
        });
    </script>
</body>
</html>
