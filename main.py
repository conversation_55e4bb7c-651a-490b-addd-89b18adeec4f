from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, HTTPException, status, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse,JSONResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.security import OAuth2<PERSON><PERSON>word<PERSON>earer, OAuth2PasswordRequestForm
from DadabaseControl.DatabaseControl import *
from ChatApi.ChatLog import *
from Utils.PromptFactory import *
from Utils.ScoringModule import *
from Utils.SceneSwitch import *
from ChatApi.ChatApi import *
from Utils.auth07082 import *
import os
import time
import uvicorn
import uuid
from datetime import datetime, timedelta
from Utils.TriggerManager import *
import json
import base64
from starlette.middleware.base import BaseHTTPMiddleware
from ChatApi.CallAction import *
from Utils.systemTools import *
import sys
from DadabaseControl.DatabaseControl2 import *
from DadabaseControl.DatabaseControl3 import *
from DadabaseControl.DatabaseControl5 import *

app = FastAPI(debug=True)

# 挂载静态资源
app.mount("/static", StaticFiles(directory="static"), name="static")
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

# 配置模板
templates = Jinja2Templates(directory="templates")

# 跨域配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 创建默认管理员账号
def create_default_admin():
    admin = get_admin_by_account07082("admin")
    if not admin:
        admin_data = {
            "account": "admin",
            "password": "admin123",
            "nickname": "系统管理员"
        }
        insert_admin07082(admin_data)
        print("已创建默认管理员账号: admin / admin123")
    else:
        print("默认管理员账号已存在，无需创建")

# 初始化数据库表结构和默认管理员
init_table()
create_default_admin()

# 认证异常处理中间件
class AuthMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request, call_next):
        try:
            response = await call_next(request)
            return response
        except HTTPException as e:
            if e.status_code == status.HTTP_401_UNAUTHORIZED:
                # API请求返回JSON
                if request.url.path.startswith("/api/"):
                    return JSONResponse(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        content={"message": "认证失败，请重新登录", "code": 401}
                    )
                # 页面请求重定向到登录页
                else:
                    return RedirectResponse(url="/login07082")
            elif e.status_code == status.HTTP_403_FORBIDDEN:
                # API请求返回JSON
                if request.url.path.startswith("/api/"):
                    return JSONResponse(
                        status_code=status.HTTP_403_FORBIDDEN,
                        content={"message": "权限不足", "code": 403}
                    )
                # 页面请求重定向到登录页
                else:
                    return RedirectResponse(url="/login07082")
            raise e

# 添加中间件
app.add_middleware(AuthMiddleware)

# 前端页面路由
@app.get("/", response_class=HTMLResponse)
async def index(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/index", response_class=HTMLResponse)
async def index2(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/login07082", response_class=HTMLResponse)
async def login_page(request: Request):
    return templates.TemplateResponse("login07082.html", {"request": request})

@app.get("/user_management07082", response_class=HTMLResponse)
async def user_management_page(request: Request):
    return templates.TemplateResponse("user_management07082.html", {"request": request})

@app.get("/system_config", response_class=HTMLResponse)
async def system_config_page(request: Request):
    return templates.TemplateResponse("system_config.html", {"request": request})

@app.get("/job_classification", response_class=HTMLResponse)
async def job_classification(request: Request):
    return templates.TemplateResponse("job_classification.html", {"request": request})

@app.get("/positions", response_class=HTMLResponse)
async def positions(request: Request):
    return templates.TemplateResponse("positions.html", {"request": request})

@app.get("/batch_management", response_class=HTMLResponse)
async def batch_management_page(request: Request):
    return templates.TemplateResponse("batch_management.html", {"request": request})

@app.get("/positions/all", response_class=JSONResponse)
async def main_get_all_positions(request: Request,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type == "admin":
        positions = get_all_positions("")
    else:
        positions = get_all_positions(current_user.id)
    return positions

@app.get("/candidates", response_class=HTMLResponse)
async def candidates(request: Request):
    return templates.TemplateResponse("candidates.html", {"request": request})

@app.get("/scenes", response_class=HTMLResponse)
async def scenes(request: Request):
    return templates.TemplateResponse("scenes.html", {"request": request})

@app.get("/interview", response_class=HTMLResponse)
async def interview(request: Request):
    return templates.TemplateResponse("interview.html", {"request": request})

@app.get("/interview/start", response_class=HTMLResponse)
async def interview_start(request: Request):
    candidate_id = request.query_params.get("candidateId")
    return templates.TemplateResponse("interview_start.html", {"request": request, "candidateId": candidate_id})

@app.get("/triggers", response_class=HTMLResponse)
async def triggers(request: Request):
    return templates.TemplateResponse("triggers.html", {"request": request})

@app.get("/classifier", response_class=HTMLResponse)
async def classifier(request: Request):
    return templates.TemplateResponse("classifier.html", {"request": request})

@app.get("/ai_brain", response_class=HTMLResponse)
async def ai_brain(request: Request):
    return templates.TemplateResponse("ai_brain.html", {"request": request})

@app.get("/virtual_hr", response_class=HTMLResponse)
async def virtual_hr(request: Request):
    return templates.TemplateResponse("virtual_hr.html", {"request": request})

@app.get("/channel_management")
def channel_management():
    return templates.TemplateResponse("channel_management.html", {"request": {}})

@app.get("/global_settings")
def global_settings():
    return templates.TemplateResponse("global_settings.html", {"request": {}})

@app.get("/scheduled_analysis", response_class=HTMLResponse)
async def scheduled_analysis(request: Request):
    return templates.TemplateResponse("scheduled_analysis.html", {"request": request})

@app.get("/qa_engine", response_class=HTMLResponse)
async def qa_engine(request: Request):
    return templates.TemplateResponse("qa_engine.html", {"request": request})

@app.get("/test_x6", response_class=HTMLResponse)
async def test_x6(request: Request):
    return templates.TemplateResponse("test_x6.html", {"request": request})

@app.get("/users/all", response_class=JSONResponse)
async def main_get_all_users(request: Request,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type == "admin":
        users = get_all_users("")
    else:
        users = get_all_users(current_user.id)
    return users


@app.get("/config", response_class=JSONResponse)
async def get_config(request: Request,current_user: TokenData = Depends(get_any_user)):
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        return {"data": config, "code": 200,"message": "获取配置成功"}
    except Exception as e:
        return {"message": f"错误: {str(e)}", "code": 400}

@app.get("/api/config", response_class=JSONResponse)
async def get_api_config(request: Request,current_user: TokenData = Depends(get_any_user)):
    return await get_config(request=request,current_user=current_user)

@app.post("/update-config", response_class=JSONResponse)
async def update_config_field(request: Request,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type != "admin":
        return {"message": "权限不足", "code": 403}
    try:
        data = await request.json()
        print(f"Received config update request with data: {data}")
        
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # Update each field in the request
        for key, value in data.items():
            if key in config:
                config[key] = value
                print(f"Updated {key} in config")
            else:
                config[key] = value
                print(f"Added new field {key} to config")
        
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
            
        print(f"Successfully saved updated config to config.json")
        return {"message": "配置文件更新成功", "code": 200}
    except Exception as e:
        print(f"Error updating configuration: {str(e)}")
        return {"message": f"错误: {str(e)}", "code": 400}

# 用户登录API
@app.post("/api/login07082", response_class=JSONResponse)
async def login_api(request: Request):
    check_bool, check_result = check_license()
    if not check_bool:
        return {"code": 400, "message": check_result}

    try:
        data = await request.json()
        account = data.get("account")
        password = data.get("password")
        user_type = data.get("user_type")
        
        if not account or not password or not user_type:
            return {"message": "账号、密码或用户类型不能为空", "code": 400}
        
        # 验证登录
        user = verify_login07082(account, password, user_type)
        if user:
            # 创建访问令牌
            access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
            access_token = create_access_token(
                data={
                    "sub": user["id"],
                    "account": user["account"],
                    "user_type": user_type
                },
                expires_delta=access_token_expires
            )
            
            # 为了安全，移除敏感信息
            if "password" in user:
                del user["password"]
            if "salt" in user:
                del user["salt"]
            
            return {
                "message": "登录成功", 
                "code": 200, 
                "data": user,
                "user_type": user_type,
                "access_token": access_token,
                "token_type": "bearer"
            }
        else:
            return {"message": "账号或密码错误", "code": 401}
    except Exception as e:
        print(f"登录失败: {str(e)}")
        return {"message": f"登录失败: {str(e)}", "code": 500}

@app.put("/api/config/openai", response_class=JSONResponse)
async def update_openai_config(request: Request,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type != "admin":
        return {"message": "权限不足", "code": 403}
    try:
        data = await request.json()
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        config['openai_config'] = data['openai_config']
        
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
            
        return {"message": "OpenAI配置成功","code": 200}
    except Exception as e:
        return {"message": f"错误: {str(e)}", "code": 400}

@app.put("/api/config/prompt", response_class=JSONResponse)
async def update_prompt_config(request: Request,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type != "admin":
        return {"message": "权限不足", "code": 400}
    try:
        data = await request.json()
        print(f"Received prompt update request with data: {data}")
        
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        if 'default_prompt' in data:
            config['default_prompt'] = data['default_prompt']
            print(f"Updated default_prompt")
        
        if 'position_prompt' in data:
            config['position_prompt'] = data['position_prompt']
            print(f"Updated position_prompt")
            
        if 'scene_prompt' in data:
            config['scene_prompt'] = data['scene_prompt']
            print(f"Updated scene_prompt")
            
        if 'user_info_prompt' in data:
            config['user_info_prompt'] = data['user_info_prompt']
            print(f"Updated user_info_prompt")
        
        if 'scoring_rules_prompt' in data:
            config['scoring_rules_prompt'] = data['scoring_rules_prompt']
            print(f"Updated scoring_rules_prompt")
        
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
            
        print(f"Successfully saved updated config to config.json")
        return {"message": "保存成功","code": 200}
    except Exception as e:
        print(f"Error updating prompt configuration: {str(e)}")
        return {"message": f"错误: {str(e)}", "code": 400}

# 管理员API
@app.get("/api/admins07082", response_class=JSONResponse)
async def get_all_admins_api(current_user: TokenData = Depends(get_admin_user)):
    try:
        admins = get_all_admins07082()
        # 为了安全，移除所有管理员的敏感信息
        for admin in admins:
            if "password" in admin:
                del admin["password"]
            if "salt" in admin:
                del admin["salt"]
        
        return admins
    except Exception as e:
        print(f"获取所有管理员失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取管理员列表失败: {str(e)}")

@app.get("/api/admin07082/{admin_id}", response_class=JSONResponse)
async def get_admin_by_id_api(admin_id: str, current_user: TokenData = Depends(get_admin_user)):
    try:
        admin = get_admin07082(admin_id)
        if admin:
            # 为了安全，移除敏感信息
            if "password" in admin:
                del admin["password"]
            if "salt" in admin:
                del admin["salt"]
            
            return {"code": 200, "message": "获取管理员成功", "data": admin}
        else:
            return {"code": 404, "message": f"未找到ID为 {admin_id} 的管理员"}
    except Exception as e:
        print(f"获取管理员失败: {str(e)}")
        return {"code": 500, "message": f"获取管理员失败: {str(e)}"}

@app.post("/api/admin07082", response_class=JSONResponse)
async def add_admin_api(request: Request, current_user: TokenData = Depends(get_admin_user)):
    try:
        data = await request.json()
        
        # 验证必填字段
        if not data.get("account") or not data.get("password"):
            return {"code": 400, "message": "账号和密码不能为空"}
            
        # 检查账号是否已存在
        existing_admin = get_admin_by_account07082(data["account"])
        if existing_admin:
            return {"code": 400, "message": f"账号 {data['account']} 已存在"}
        
        # 添加管理员
        admin_id = insert_admin07082(data)
        if admin_id:
            return {"code": 200, "message": "添加管理员成功", "data": {"id": admin_id}}
        else:
            return {"code": 500, "message": "添加管理员失败"}
    except Exception as e:
        print(f"添加管理员失败: {str(e)}")
        return {"code": 500, "message": f"添加管理员失败: {str(e)}"}

@app.put("/api/admin07082/{admin_id}", response_class=JSONResponse)
async def update_admin_api(admin_id: str, request: Request, current_user: TokenData = Depends(get_admin_user)):
    try:
        data = await request.json()
        
        # 检查管理员是否存在
        admin = get_admin07082(admin_id)
        if not admin:
            return {"code": 404, "message": f"未找到ID为 {admin_id} 的管理员"}
        
        # 如果要更改账号，检查新账号是否已存在
        if "account" in data and data["account"] != admin["account"]:
            existing_admin = get_admin_by_account07082(data["account"])
            if existing_admin and existing_admin["id"] != admin_id:
                return {"code": 400, "message": f"账号 {data['account']} 已存在"}
        
        # 更新管理员信息
        success = update_admin07082(admin_id, data)
        if success:
            return {"code": 200, "message": "更新管理员成功"}
        else:
            return {"code": 500, "message": "更新管理员失败"}
    except Exception as e:
        print(f"更新管理员失败: {str(e)}")
        return {"code": 500, "message": f"更新管理员失败: {str(e)}"}

@app.delete("/api/admin07082/{admin_id}", response_class=JSONResponse)
async def delete_admin_api(admin_id: str, current_user: TokenData = Depends(get_admin_user)):
    try:
        # 检查管理员是否存在
        admin = get_admin07082(admin_id)
        if not admin:
            return {"code": 404, "message": f"未找到ID为 {admin_id} 的管理员"}
        
        # 防止删除自己
        if current_user.id == admin_id:
            return {"code": 400, "message": "不能删除当前登录的管理员账户"}
            
        # 删除管理员
        success = delete_admin07082(admin_id)
        if success:
            return {"code": 200, "message": "删除管理员成功"}
        else:
            return {"code": 500, "message": "删除管理员失败"}
    except Exception as e:
        print(f"删除管理员失败: {str(e)}")
        return {"code": 500, "message": f"删除管理员失败: {str(e)}"}

# 租户API
@app.get("/api/tenants07082", response_class=JSONResponse)
async def get_all_tenants_api(current_user: TokenData = Depends(get_admin_user)):
    try:
        tenants = get_all_tenants07082()
        # 为了安全，移除所有租户的敏感信息
        for tenant in tenants:
            if "password" in tenant:
                del tenant["password"]
            if "salt" in tenant:
                del tenant["salt"]
        
        return tenants
    except Exception as e:
        print(f"获取所有租户失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取租户列表失败: {str(e)}")

@app.get("/api/tenant07082/{tenant_id}", response_class=JSONResponse)
async def get_tenant_by_id_api(tenant_id: str, current_user: TokenData = Depends(get_admin_user)):
    try:
        tenant = get_tenant07082(tenant_id)
        if tenant:
            # 为了安全，移除敏感信息
            if "password" in tenant:
                del tenant["password"]
            if "salt" in tenant:
                del tenant["salt"]
            
            return {"code": 200, "message": "获取租户成功", "data": tenant}
        else:
            return {"code": 404, "message": f"未找到ID为 {tenant_id} 的租户"}
    except Exception as e:
        print(f"获取租户失败: {str(e)}")
        return {"code": 500, "message": f"获取租户失败: {str(e)}"}

@app.post("/api/tenant07082", response_class=JSONResponse)
async def add_tenant_api(request: Request, current_user: TokenData = Depends(get_admin_user)):
    try:
        data = await request.json()
        
        # 验证必填字段
        if not data.get("account") or not data.get("password"):
            return {"code": 400, "message": "账号和密码不能为空"}
            
        # 检查账号是否已存在
        existing_tenant = get_tenant_by_account07082(data["account"])
        if existing_tenant:
            return {"code": 400, "message": f"账号 {data['account']} 已存在"}
        
        # 添加租户
        tenant_id = insert_tenant07082(data)
        if tenant_id:
            return {"code": 200, "message": "添加租户成功", "data": {"id": tenant_id}}
        else:
            return {"code": 500, "message": "添加租户失败"}
    except Exception as e:
        print(f"添加租户失败: {str(e)}")
        return {"code": 500, "message": f"添加租户失败: {str(e)}"}

@app.put("/api/tenant07082/{tenant_id}", response_class=JSONResponse)
async def update_tenant_api(tenant_id: str, request: Request, current_user: TokenData = Depends(get_admin_user)):
    try:
        data = await request.json()
        
        # 检查租户是否存在
        tenant = get_tenant07082(tenant_id)
        if not tenant:
            return {"code": 404, "message": f"未找到ID为 {tenant_id} 的租户"}
        
        # 如果要更改账号，检查新账号是否已存在
        if "account" in data and data["account"] != tenant["account"]:
            existing_tenant = get_tenant_by_account07082(data["account"])
            if existing_tenant and existing_tenant["id"] != tenant_id:
                return {"code": 400, "message": f"账号 {data['account']} 已存在"}
        
        # 更新租户信息
        success = update_tenant07082(tenant_id, data)
        if success:
            return {"code": 200, "message": "更新租户成功"}
        else:
            return {"code": 500, "message": "更新租户失败"}
    except Exception as e:
        print(f"更新租户失败: {str(e)}")
        return {"message": f"更新租户失败: {str(e)}", "code": 500}

@app.delete("/api/tenant07082/{tenant_id}", response_class=JSONResponse)
async def delete_tenant_api(tenant_id: str, current_user: TokenData = Depends(get_admin_user)):
    try:
        # 检查租户是否存在
        tenant = get_tenant07082(tenant_id)
        if not tenant:
            return {"code": 404, "message": f"未找到ID为 {tenant_id} 的租户"}
        
        # 删除租户
        success = delete_tenant07082(tenant_id)
        if success:
            return {"code": 200, "message": "删除租户成功"}
        else:
            return {"code": 500, "message": "删除租户失败"}
    except Exception as e:
        print(f"删除租户失败: {str(e)}")
        return {"code": 500, "message": f"删除租户失败: {str(e)}"}

# API端点
@app.post("/chat")
async def chat(request: Request):
    data = await request.json()
    try:
        apikey = request.headers.get("Authorization").replace("Bearer ","")
        tenant_id = get_apikey_by_value(apikey).get("tenant_id")
    except Exception as e:
        return {"code": 400, "message": "apikey not found"}
    messages = data.get("messages")
    name = data.get("name")
    userid = data.get("userid")
    is_reply = data.get("is_reply")
    print(userid,tenant_id)
    user = get_user(userid,tenant_id)
    if user is None:
        return {"code": 400, "message": "user not found"}
    current_scene_id = user.get("sceneId")

    if is_reply:
        user_message = messages[-1]
        if "timestamp" not in user_message:
            user_message["timestamp"] = int(time.time())
        
        prompt = await create_prompt(user,tenant_id)
        result_json = await get_result_json(userid,prompt,user_message,apikey,tenant_id)
        content = result_json.get("content")
        new_score = result_json.get("score")
        score_tracking = result_json.get("score_tracking")
        actions = result_json.get("actions")
        add_message = [user_message,{"role": "assistant", "content": json.dumps(result_json,ensure_ascii=False),"timestamp":int(time.time())}]
        save_chat_log(user,add_message,new_score,score_tracking,actions,channel="boss")
        update_user(userid,{"score":new_score},tenant_id)

        swith_sceneId = current_scene_id
        result_dict = call_action(userid,actions)
        if "target_scene_id" in result_dict.keys():
            swith_sceneId = result_dict["target_scene_id"]
            user["sceneId"] = swith_sceneId
        if current_scene_id != swith_sceneId:
            print("检测到切换场景,更换提示词,再次请求AI回复")
            if "activate_message" in result_dict.keys():
                user_message = {"role":"user","content":result_dict["activate_message"],"timestamp":int(time.time())}
            else:
                user_message = {"role":"user","content":"请继续吧","timestamp":int(time.time())}
            prompt_switch = await create_prompt(user,tenant_id)
            result_json_switch = await get_result_json(userid,prompt_switch,user_message,apikey,tenant_id)
            content = result_json_switch.get("content")
            # new_score = result_json_switch.get("score")
            if result_dict["score"] == 0:
                switech_score = new_score
            else:
                switech_score = result_dict["score"]
            # score_tracking = result_json_switch.get("score_tracking")
            score_tracking = {"original_score": new_score, "current_score": switech_score, "reason": [f"执行场景切换,按照指令赋予分数{switech_score}分"]}
            actions = result_json_switch.get("actions")
            result_json = result_json_switch
            result_json["score_tracking"] = score_tracking
            result_json["score"] = switech_score
            update_user(userid,{"score":switech_score},tenant_id)
            add_message = [user_message,{"role": "assistant", "content": json.dumps(result_json,ensure_ascii=False),"timestamp":int(time.time())}]
            save_chat_log(user,add_message,switech_score,score_tracking,actions,channel="boss")
            new_score = switech_score
        return {"code": 200, "message": "success", "score": new_score, "name": name,"content":content,"is_reply": True,"sence":get_scene(swith_sceneId),"actions":actions,"score_tracking":score_tracking,"channel":"boss"}
    else:
        return {"code": 200,"message": "success","score":get_user(userid,tenant_id).get("score"),"name":name,"content":"","is_reply":False,"sence":get_scene(current_scene_id),"actions":[]}

@app.get("/chat/history/{userid}", response_class=JSONResponse)
async def main_get_chat_history(userid: str,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type == "admin":
        user = get_user(userid,"")
    else:
        user = get_user(userid,current_user.id)
    if user is None:
        return {"code": 400, "message": "user not found"}
    
    chat_logs = get_chatlogs_by_user(user.get("userid"))
    for chat_log in chat_logs:
        if chat_log["role"] == "assistant":
            # 判断chat_log["content"]是否是字典
            content_data = None
            if isinstance(chat_log["content"], dict):
                content_data = chat_log["content"]
                chat_log["content"] = content_data["content"]
                # 保存分数跟踪数据（如果存在）
                if "score_tracking" in content_data:
                    chat_log["score_tracking"] = content_data["score_tracking"]
                # 保存actions数据（如果存在）
                if "actions" in content_data:
                    chat_log["actions"] = content_data["actions"]
            else:
                try:
                    content_data = json.loads(chat_log["content"].replace("```json","").replace("```",""))
                    chat_log["content"] = content_data["content"]
                    # 保存分数跟踪数据（如果存在）
                    if "score_tracking" in content_data:
                        chat_log["score_tracking"] = content_data["score_tracking"]
                    # 保存actions数据（如果存在）
                    if "actions" in content_data:
                        chat_log["actions"] = content_data["actions"]
                except:
                    # 如果解析失败，直接使用内容
                    pass
    return chat_logs

@app.delete("/chat/history/{userid}")
async def main_delete_chat_history(userid: str,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type == "admin":
        user = get_user(userid,"")
    else:
        user = get_user(userid,current_user.id)
    if user is None:
        return {"code": 400, "message": "user not found"}
    delete_chatlogs_by_user(user.get("userid"))
    return {"code": 200, "message": "success"}

@app.post("/create_user")
async def create_user(request: Request):
    data = await request.json()
    apikey = request.headers.get("Authorization").replace("Bearer ","")
    tenant_id = get_apikey_by_value(apikey).get("tenant_id")
    if tenant_id is None:
        return {"code": 400, "message": "apikey not found"}
    # try:
    try:
        userid = data.get("userid")
    except:
        userid = str(uuid.uuid4())
    positionId = data.get("positionId")
    name = data.get("name")
    phone = data.get("phone")
    resume = data.get("resume")
    wechat = data.get("wechat")
    city = data.get("city")
    address = data.get("address")
    source = data.get("source")
    gender = data.get("gender")
    age = data.get("age")
    real_name = data.get("real_name")
    wechat_nickname = data.get("wechat_nickname")
    remarks = data.get("remarks")
    title = data.get("title")
    education_experience = data.get("education_experience")
    if source == "wechat":
        score = 30
    else:
        score = 0
    position = get_position(positionId,tenant_id)
    if position is None:
        return {"code": 400, "message": "position not found"}
    job_classification_id = position.get("job_classification_id")
    virtual_hr_id = position.get("virtual_hr_id")
    
    data = {
        "userid": userid,
        "positionId": positionId,
        "name": name,
        "phone": phone,
        "resume": resume,
        "wechat": wechat,
        "city": city,
        "address": address,
        "score": score,
        "sceneId": get_default_scene(job_classification_id).get("sceneId"),
        "virtual_hr_id": virtual_hr_id,
        "gender": gender,
        "age": age,
        "real_name": real_name,
        "wechat_nickname": wechat_nickname,
        "remarks": remarks,
        "title": title,
        "education_experience": education_experience,
        "tenant_id": tenant_id
    }
    
    if "resume" in data:
        try:
            resume = base64.b64decode(data.get("resume"))
            with open(f"resume/{userid}.pdf", "wb") as f:
                f.write(resume)
            data["resume"] = f"{userid}.pdf"
        except:
            pass
    
    if not userid:
        userid = insert_user(data)
        return {"code": 200, "message": "success", "user_id": userid}
    if not get_user(userid):
        userid = insert_user(data)
        return {"code": 200, "message": "success", "user_id": userid}
    else:
        return {"code": 400, "message": "user already exists or userid is empty"}
    # except Exception as e:
    #     if "'NoneType' object has no attribute" in str(e):
    #         return {"code": 400, "message": "检测到职位分类未设置场景，请先设置场景 " + str(e)}
    #     else:
    #         return {"code": 400, "message": str(e)}

@app.post("/web_create_user")
async def web_create_user(request: Request,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type != "tenant":
        return {"code": 400, "message": "只有租户可以创建求职者"}
    data = await request.json()
    try:
        try:
            userid = data.get("userid")
        except:
            userid = str(uuid.uuid4())
        positionId = data.get("positionId")
        name = data.get("name")
        phone = data.get("phone")
        resume = data.get("resume")
        wechat = data.get("wechat")
        city = data.get("city")
        address = data.get("address")
        source = data.get("source")
        gender = data.get("gender")
        age = data.get("age")
        real_name = data.get("real_name")
        wechat_nickname = data.get("wechat_nickname")
        remarks = data.get("remarks")
        title = data.get("title")
        education_experience = data.get("education_experience")
        if source == "wechat":
            score = 30
        else:
            score = 0
        position = get_position(positionId,current_user.id)
        job_classification_id = position.get("job_classification_id")
        virtual_hr_id = position.get("virtual_hr_id")
        
        data = {
            "userid": userid,
            "positionId": positionId,
            "name": name,
            "phone": phone,
            "resume": resume,
            "wechat": wechat,
            "city": city,
            "address": address,
            "score": score,
            "sceneId": get_default_scene(job_classification_id).get("sceneId"),
            "virtual_hr_id": virtual_hr_id,
            "gender": gender,
            "age": age,
            "real_name": real_name,
            "wechat_nickname": wechat_nickname,
            "remarks": remarks,
            "title": title,
            "education_experience": education_experience,
            "tenant_id": current_user.id
        }
        
        if "resume" in data:
            try:
                resume = base64.b64decode(data.get("resume"))
                with open(f"resume/{userid}.pdf", "wb") as f:
                    f.write(resume)
                data["resume"] = f"{userid}.pdf"
            except:
                pass
        
        if not userid:
            userid = insert_user(data)
            return {"code": 200, "message": "success", "user_id": userid}
        if not get_user(userid,current_user.id):
            userid = insert_user(data)
            return {"code": 200, "message": "success", "user_id": userid}
        else:
            return {"code": 400, "message": "user already exists or userid is empty"}
    except Exception as e:
        if "'NoneType' object has no attribute" in str(e):
            return {"code": 400, "message": "检测到职位分类未设置场景，请先设置场景"}
        else:
            return {"code": 400, "message": str(e)}

# apikey 获取职位
@app.get("/position/{position_id}")
async def main_get_position(position_id: str,request: Request):
    apikey = request.headers.get("Authorization").replace("Bearer ","")
    tenant_id = get_apikey_by_value(apikey).get("tenant_id")
    if tenant_id is None:
        return {"code": 400, "message": "apikey not found"}
    position = get_position(position_id,tenant_id)
    return position

# 租户获取职位
@app.get("/web_position/{position_id}")
async def web_main_get_position(position_id: str,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type == "admin":
        position = get_position(position_id,"")
    else:
        position = get_position(position_id,current_user.id)
    return position

# apikey 添加职位
@app.post("/position")
async def add_position(request: Request):
    data = await request.json()
    apikey = request.headers.get("Authorization").replace("Bearer ","")
    tenant_id = get_apikey_by_value(apikey).get("tenant_id")
    if tenant_id is None:
        return {"code": 400, "message": "apikey not found"}
    data["tenant_id"] = tenant_id
    if "enable_boss_scene" not in data:
        data["enable_boss_scene"] = 1
    success,message = insert_position(data)
    if success:
        return {"code": 200, "message": "添加职位成功","position_id":message}
    else:
        return {"code": 400, "message": message}

# 租户添加职位
@app.post("/web_position")
async def web_add_position(request: Request,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type != "tenant":
        return {"code": 400, "message": "只有租户可以添加职位"}
    data = await request.json()
    data["tenant_id"] = current_user.id
    if "enable_boss_scene" not in data:
        data["enable_boss_scene"] = 1
    success,message = insert_position(data)
    if success:
        return {"code": 200, "message": "添加职位成功","position_id":message}
    else:
        return {"code": 400, "message": message}

# apikey 更新职位
@app.put("/position/{position_id}")
async def main_update_position(position_id: str, request: Request):
    data = await request.json()
    apikey = request.headers.get("Authorization").replace("Bearer ","")
    tenant_id = get_apikey_by_value(apikey).get("tenant_id")
    if tenant_id is None:
        return {"code": 400, "message": "apikey not found"}
    data["tenant_id"] = tenant_id
    if update_position(position_id, data):
        return {"code": 200, "message": "更新职位成功"}
    else:
        return {"code": 400, "message": "未找到dataId对应的数据"}

# 租户更新职位
@app.put("/web_position/{position_id}")
async def web_update_position(position_id: str, request: Request,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type != "tenant":
        return {"code": 400, "message": "只有租户可以更新职位"}
    data = await request.json()
    data["tenant_id"] = current_user.id
    if update_position(position_id, data):
        return {"code": 200, "message": "更新职位成功"}
    else:
        return {"code": 400, "message": "未找到dataId对应的数据"}

# apikey 删除职位
@app.delete("/position/{position_id}")
async def main_delete_position(position_id: str,request: Request):
    apikey = request.headers.get("Authorization").replace("Bearer ","")
    tenant_id = get_apikey_by_value(apikey).get("tenant_id")
    if tenant_id is None:
        return {"code": 400, "message": "apikey not found"}
    delete_position(position_id,tenant_id)
    return {"code": 200, "message": "删除职位成功"}
# 租户删除职位
@app.delete("/web_position/{position_id}")
async def web_delete_position(position_id: str,request: Request,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type != "tenant":
        return {"code": 400, "message": "只有租户可以删除职位"}
    delete_position(position_id,current_user.id)
    return {"code": 200, "message": "删除职位成功"}

# @app.get("/users")
# async def get_users():
#     users = get_all_users()
#     return users

@app.get("/user/{user_id}")
async def get_user_by_id(user_id: str,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type == "admin":
        user = get_user(user_id,"")
    else:
        user = get_user(user_id,current_user.id)
    return user

# apikey 更新用户
@app.put("/user/{user_id}")
async def main_update_user(user_id: str, request: Request):
    data = await request.json()
    apikey = request.headers.get("Authorization").replace("Bearer ","")
    tenant_id = get_apikey_by_value(apikey).get("tenant_id")
    if tenant_id is None:
        return {"code": 400, "message": "apikey not found"}
    data["tenant_id"] = tenant_id
    #  判断resume在不在data里
    if "resume" in data:
        resume = data.get("resume")
        if resume:
            try:
                resume = base64.b64decode(resume)
                with open(f"resume/{user_id}.pdf", "wb") as f:
                    f.write(resume)
                    data["resume"] = f"{user_id}.pdf"
            except:
                pass
        if "resume_text" in data:
            resume_text = data.get("resume_text")
            if resume_text:
                data["resume_text"] = json.dumps(resume_text,ensure_ascii=False)
        if "positionId" in data:
            position = get_position(data.get("positionId"),tenant_id)
            if position:
                data["virtual_hr_id"] = position.get("virtual_hr_id")
    update_user(user_id, data,tenant_id)
    return {"code": 200, "message": "更新候选人成功"}

# 租户更新用户
@app.put("/web_user/{user_id}")
async def web_update_user(user_id: str, request: Request,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type != "tenant":
        return {"code": 400, "message": "只有租户可以更新用户"}
    data = await request.json()
    data["tenant_id"] = current_user.id
    #  判断resume在不在data里
    if "resume" in data:
        resume = data.get("resume")
        if resume:
            try:
                resume = base64.b64decode(resume)
                with open(f"resume/{user_id}.pdf", "wb") as f:
                    f.write(resume)
                    data["resume"] = f"{user_id}.pdf"
            except:
                pass
        if "resume_text" in data:
            resume_text = data.get("resume_text")
            if resume_text:
                data["resume_text"] = json.dumps(resume_text,ensure_ascii=False)
        if "positionId" in data:
            position = get_position(data.get("positionId"),current_user.id)
            if position:
                data["virtual_hr_id"] = position.get("virtual_hr_id")
    update_user(user_id, data,current_user.id)
    return {"code": 200, "message": "更新候选人成功"}

@app.put("/user/{user_id}/score")
async def update_user_score07081(user_id: str, request: Request,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type != "tenant":
        return {"code": 400, "message": "只有租户可以更新用户分数"}
    try:
        data = await request.json()
        if "score" not in data:
            return {"code": 400, "message": "缺少分数参数"}
        update_user(user_id, {"score": data["score"]},current_user.id)
        return {"code": 200, "message": "更新候选人分数成功"}
    except Exception as e:
        return {"code": 500, "message": f"更新候选人分数失败: {str(e)}"}

@app.put("/user/{user_id}/scene")
async def update_user_scene07081(user_id: str, request: Request,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type != "tenant":
        return {"code": 400, "message": "只有租户可以更新用户场景"}
    try:
        data = await request.json()
        if "sceneId" not in data:
            return {"code": 400, "message": "缺少场景ID参数"}
        
        update_user(user_id, {"sceneId": data["sceneId"]},current_user.id)
        return {"code": 200, "message": "更新候选人场景成功"}
    except Exception as e:
        return {"code": 500, "message": f"更新候选人场景失败: {str(e)}"}

@app.delete("/user/{user_id}")
async def main_delete_user(user_id: str,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type != "tenant":
        return {"code": 400, "message": "只有租户可以删除用户"}
    delete_user(user_id,current_user.id)
    return {"code": 200, "message": "删除候选人成功"}

# apikey 获取职位分类
@app.get("/job_classifications")
async def get_job_classifications(request: Request):
    apikey = request.headers.get("Authorization").replace("Bearer ","")
    tenant_id = get_apikey_by_value(apikey).get("tenant_id")
    if tenant_id is None:
        return {"code": 400, "message": "apikey not found"}
    job_classifications = get_all_job_classifications(tenant_id)
    return {"code": 200, "data":job_classifications}

# 租户获取职位分类
@app.get("/web_job_classifications")
async def web_get_job_classifications(request: Request,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type == "admin":
        job_classifications = get_all_job_classifications("")
    else:
        job_classifications = get_all_job_classifications(current_user.id)
    return {"code": 200, "data":job_classifications}


@app.get("/job_classifications/{id}")
async def get_job_classification_by_id(id: str,current_user: TokenData = Depends(get_any_user)):
    if current_user.user_type == "admin":
        job_classification = get_job_classification(id,"")
    else:
        job_classification = get_job_classification(id,current_user.id)
    if job_classification:
        return {"code": 200, "data": job_classification, "message": "获取职位分类成功"}
    else:
        return {"code": 404, "message": "职位分类不存在"}


from main2 import *
from main3 import *
from main4 import *

if not check_lisence():
    sys.exit(0)

# 初始化数据库表
from DadabaseControl.DatabaseControl4 import init_analysis_tables
init_analysis_tables()

# 执行数据库迁移
migrate_database()

# 定义应用启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化"""
    # 启动定时任务调度器
    from Utils.SchedulerManager import start_scheduler
    start_scheduler()
    print("应用启动完成，定时任务调度器已启动")

if __name__ == "__main__":
    uvicorn.run(app, host="127.0.0.1", port=8000)